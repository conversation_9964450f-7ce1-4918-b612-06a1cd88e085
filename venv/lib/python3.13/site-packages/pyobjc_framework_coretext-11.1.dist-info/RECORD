CoreText/__init__.py,sha256=dU-9MTRBDKyRmTQttctAiqsM1DWcn-wFhCrJe9xeG48,886
CoreText/__pycache__/__init__.cpython-313.pyc,,
CoreText/__pycache__/_metadata.cpython-313.pyc,,
CoreText/_manual.cpython-313-darwin.so,sha256=4IW-M0aUMe4lMkmVQpe9GkZYihfDAIL7Qk97wOyFYNE,87472
CoreText/_metadata.py,sha256=22dJxZbTrj173mkoAJjG_XOdDk-HO0OIEIOwKoPO-8U,78471
pyobjc_framework_coretext-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_coretext-11.1.dist-info/METADATA,sha256=MQBeLI8paMP8Da3LZw17py7bqY-tkPg6IcWhaO2EmNw,2670
pyobjc_framework_coretext-11.1.dist-info/RECORD,,
pyobjc_framework_coretext-11.1.dist-info/WHEEL,sha256=memlX0NSEQnmSMa3rcNWPnk4cttudwgAZx3qq8qO4ME,115
pyobjc_framework_coretext-11.1.dist-info/licenses/LICENSE.txt,sha256=DKBLB5KNSHK52bsiGHygQm3Yv6sI8m6toJmacdyBqv8,1249
pyobjc_framework_coretext-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_coretext-11.1.dist-info/top_level.txt,sha256=el7Md_M_JEGtUwR5FVoJ75sdEa89FGQ8-h7eR2u8pAo,9
