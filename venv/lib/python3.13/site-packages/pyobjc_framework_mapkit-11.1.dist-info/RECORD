MapKit/_MapKit.cpython-313-darwin.so,sha256=ck8e9XiNZmSANbyaDJLY8qaUCTOwPst8rB6UYisiUFs,86576
MapKit/__init__.py,sha256=3nwJANkCvk4vMXgllFBWn77wnnOfsHKQOJdThBZK4FM,1617
MapKit/__pycache__/__init__.cpython-313.pyc,,
MapKit/__pycache__/_metadata.cpython-313.pyc,,
MapKit/_inlines.cpython-313-darwin.so,sha256=vSUeacE9kj8FUrxBvILZAgRELtpMhucolEuwqlkeRjE,69584
MapKit/_metadata.py,sha256=CyjpgEt0uFRPsqehH5spLNGR0Hnwj2E8O_kImpDNxa8,54167
pyobjc_framework_mapkit-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_mapkit-11.1.dist-info/METADATA,sha256=jfgsN83fPM_Sxm_rkylSGmqp7OUekBT7yGHqrfTCSzw,2559
pyobjc_framework_mapkit-11.1.dist-info/RECORD,,
pyobjc_framework_mapkit-11.1.dist-info/WHEEL,sha256=memlX0NSEQnmSMa3rcNWPnk4cttudwgAZx3qq8qO4ME,115
pyobjc_framework_mapkit-11.1.dist-info/licenses/LICENSE.txt,sha256=DKBLB5KNSHK52bsiGHygQm3Yv6sI8m6toJmacdyBqv8,1249
pyobjc_framework_mapkit-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_mapkit-11.1.dist-info/top_level.txt,sha256=4NuNAfnKaphl_fUSXNL6v4sX4lPRUkCgtZ9VMDlsUAk,7
