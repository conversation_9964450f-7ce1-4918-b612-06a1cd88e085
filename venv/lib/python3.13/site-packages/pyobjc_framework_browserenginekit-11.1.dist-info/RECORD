BrowserEngineKit/_BrowserEngineKit.cpython-313-darwin.so,sha256=N8pVqr0C-7UB88SXZgn-kLVrAhsJxJcAFPo0cnqsAiU,68808
BrowserEngineKit/__init__.py,sha256=4X7rGAhTKoMZXyIReY_dFRyuliG3kpQZ1IfIWALpfgM,2460
BrowserEngineKit/__pycache__/__init__.cpython-313.pyc,,
BrowserEngineKit/__pycache__/_metadata.cpython-313.pyc,,
BrowserEngineKit/_metadata.py,sha256=K3CVTMtVI0G-hTEe2Ygwh0XezZDFjqHOtI4yy1pcMA4,22101
pyobjc_framework_browserenginekit-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_browserenginekit-11.1.dist-info/METADATA,sha256=irPzlxDRUeaYbEVrWzYVfh1IN7gUOjWtTsgK4CrFDSs,2596
pyobjc_framework_browserenginekit-11.1.dist-info/RECORD,,
pyobjc_framework_browserenginekit-11.1.dist-info/WHEEL,sha256=memlX0NSEQnmSMa3rcNWPnk4cttudwgAZx3qq8qO4ME,115
pyobjc_framework_browserenginekit-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_browserenginekit-11.1.dist-info/top_level.txt,sha256=M9rbiFwQqCiKaNR1bCgoYBlrxO4ZFr7pDjLAaa6kGtQ,17
