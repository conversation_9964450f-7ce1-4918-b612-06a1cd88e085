SyncServices/_SyncServices.cpython-313-darwin.so,sha256=YiuIj-La5orpTnOYXLqSPIp18i8Mj3zW8EpAume6AL8,85936
SyncServices/__init__.py,sha256=pBxspCuCYtJRs5h_-lRbmpTpmE9ctxDVxno55ZgBsik,915
SyncServices/__pycache__/__init__.cpython-313.pyc,,
SyncServices/__pycache__/_metadata.cpython-313.pyc,,
SyncServices/_metadata.py,sha256=21SSNXmQkPbUP4eikPhIOfUM8GyPl7jV5Een4jhsGpg,21693
pyobjc_framework_syncservices-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_syncservices-11.1.dist-info/METADATA,sha256=mciUK91FhI52S1zJIU2l3wIkEaNtVKebGSchqSceB0U,2805
pyobjc_framework_syncservices-11.1.dist-info/RECORD,,
pyobjc_framework_syncservices-11.1.dist-info/WHEEL,sha256=memlX0NSEQnmSMa3rcNWPnk4cttudwgAZx3qq8qO4ME,115
pyobjc_framework_syncservices-11.1.dist-info/licenses/LICENSE.txt,sha256=DKBLB5KNSHK52bsiGHygQm3Yv6sI8m6toJmacdyBqv8,1249
pyobjc_framework_syncservices-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_syncservices-11.1.dist-info/top_level.txt,sha256=OWWKyy6xR4iQps-xA51Q27RW0bzcqSHsJLAufQoFJw0,13
