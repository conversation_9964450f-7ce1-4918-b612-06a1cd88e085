Cinematic/__init__.py,sha256=oE8Xcnh_Z5Ef-Nhd81MSB5QeoAYV4taRiSKqb-0LN80,1864
Cinematic/__pycache__/__init__.cpython-313.pyc,,
Cinematic/__pycache__/_metadata.cpython-313.pyc,,
Cinematic/_metadata.py,sha256=3fsQdJ7WyS404WX8ZT-7cUjSeb1IBH91PyjKFkw_Jh0,6648
pyobjc_framework_cinematic-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_cinematic-11.1.dist-info/METADATA,sha256=BMOjwJgFzYdpemyyT50oKcvt4KawK9nVpOeL-b4V0bA,2570
pyobjc_framework_cinematic-11.1.dist-info/RECORD,,
pyobjc_framework_cinematic-11.1.dist-info/WHEEL,sha256=JNWh1Fm1UdwIQV075glCn4MVuCRs0sotJIq-J6rbxCU,109
pyobjc_framework_cinematic-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_cinematic-11.1.dist-info/top_level.txt,sha256=Ejp7ydokXlp1KWtd-5vynMm8RKH0wGgphfq5fVeAFWI,10
