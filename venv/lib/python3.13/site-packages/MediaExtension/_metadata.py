# This file is generated by objective.metadata
#
# Last update: Fri Nov 15 12:21:31 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$MERAWProcessorReadyForMoreMediaDataDidChangeNotification$MERAWProcessorValuesDidChangeNotification$MEVideoDecoderReadyForMoreMediaDataDidChangeNotification$MediaExtensionErrorDomain$"""
enums = """$MEDecodeFrameFrameDropped@1$MEDecodeFrameNoStatus@0$MEErrorAllocationFailure@-19321$MEErrorEndOfStream@-19329$MEErrorInternalFailure@-19324$MEErrorInvalidParameter@-19322$MEErrorLocationNotAvailable@-19328$MEErrorNoSamples@-19327$MEErrorNoSuchEdit@-19326$MEErrorParsingFailure@-19323$MEErrorPermissionDenied@-19330$MEErrorPropertyNotSupported@-19325$MEErrorReferenceMissing@-19331$MEErrorUnsupportedFeature@-19320$MEFileInfoContainsFragments@1$MEFileInfoCouldContainButDoesNotContainFragments@2$MEFileInfoCouldNotContainFragments@0$MEFormatReaderParseAdditionalFragmentsStatusFragmentAdded@2$MEFormatReaderParseAdditionalFragmentsStatusFragmentsComplete@4$MEFormatReaderParseAdditionalFragmentsStatusSizeIncreased@1$"""
misc.update(
    {
        "MEFileInfoFragmentsStatus": NewType("MEFileInfoFragmentsStatus", int),
        "MEFormatReaderParseAdditionalFragmentsStatus": NewType(
            "MEFormatReaderParseAdditionalFragmentsStatus", int
        ),
        "MEDecodeFrameStatus": NewType("MEDecodeFrameStatus", int),
        "MEError": NewType("MEError", int),
    }
)
misc.update({})
misc.update(
    {
        "kMEVideoDecoderCodecInfoKey": "CodecInfo",
        "kMEVideoDecoderCodecNameKey": "CodecName",
        "kMEVideoDecoderClassImplementationIDKey": "ClassImplementationID",
        "kMEVideoDecoderObjectNameKey": "ObjectName",
        "kMERAWProcessorExtensionPointName": "com.apple.mediaextension.rawprocessor",
        "kMEVideoDecoderCodecTypeKey": "CodecType",
        "kMEFormatReaderUTTypeArrayKey": "MTUTTypeArray",
        "kMEFormatReaderFileNameExtensionArrayKey": "MTFileNameExtensionArray",
        "kMEFormatReaderExtensionPointName": "com.apple.mediaextension.formatreader",
        "kMEFormatReaderClassImplementationIDKey": "ClassImplementationID",
        "kMEFormatReaderObjectNameKey": "ObjectName",
        "kMEVideoDecoderExtensionPointName": "com.apple.mediaextension.videodecoder",
    }
)
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"MEByteSource",
        b"byteSourceForRelatedFileName:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"MEByteSource",
        b"readDataOfLength:fromOffset:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"MEByteSource",
        b"readDataOfLength:fromOffset:toDestination:bytesRead:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                4: {"type_modifier": b"o", "c_array_length_in_arg": 2},
                5: {"type_modifier": b"o"},
                6: {"type_modifier": b"o"},
            },
        },
    )
    r(
        b"MEByteSource",
        b"readDataOfLength:fromOffset:toDestination:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"MEDecodeFrameOptions", b"doNotOutputFrame", {"retval": {"type": b"Z"}})
    r(b"MEDecodeFrameOptions", b"realTimePlayback", {"retval": {"type": b"Z"}})
    r(
        b"MEDecodeFrameOptions",
        b"setDoNotOutputFrame:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MEDecodeFrameOptions",
        b"setRealTimePlayback:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MEFormatReaderInstantiationOptions",
        b"allowIncrementalFragmentParsing",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MEHEVCDependencyInfo",
        b"hasStepwiseTemporalSubLayerAccess",
        {"retval": {"type": b"Z"}},
    )
    r(b"MEHEVCDependencyInfo", b"hasTemporalSubLayerAccess", {"retval": {"type": b"Z"}})
    r(
        b"MEHEVCDependencyInfo",
        b"setStepwiseTemporalSubLayerAccess:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MEHEVCDependencyInfo",
        b"setTemporalSubLayerAccess:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"MERAWProcessingBooleanParameter", b"cameraValue", {"retval": {"type": b"Z"}})
    r(b"MERAWProcessingBooleanParameter", b"currentValue", {"retval": {"type": b"Z"}})
    r(
        b"MERAWProcessingBooleanParameter",
        b"hasCameraValue:",
        {
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"^Z", "type_modifier": b"o"}},
        },
    )
    r(
        b"MERAWProcessingBooleanParameter",
        b"hasNeutralValue:",
        {
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"^Z", "type_modifier": b"o"}},
        },
    )
    r(
        b"MERAWProcessingBooleanParameter",
        b"initWithName:key:description:initialValue:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(
        b"MERAWProcessingBooleanParameter",
        b"initWithName:key:description:initialValue:cameraValue:",
        {"arguments": {5: {"type": b"Z"}, 6: {"type": b"Z"}}},
    )
    r(
        b"MERAWProcessingBooleanParameter",
        b"initWithName:key:description:initialValue:neutralValue:",
        {"arguments": {5: {"type": b"Z"}, 6: {"type": b"Z"}}},
    )
    r(
        b"MERAWProcessingBooleanParameter",
        b"initWithName:key:description:initialValue:neutralValue:cameraValue:",
        {"arguments": {5: {"type": b"Z"}, 6: {"type": b"Z"}, 7: {"type": b"Z"}}},
    )
    r(b"MERAWProcessingBooleanParameter", b"initialValue", {"retval": {"type": b"Z"}})
    r(b"MERAWProcessingBooleanParameter", b"neutralValue", {"retval": {"type": b"Z"}})
    r(
        b"MERAWProcessingBooleanParameter",
        b"setCurrentValue:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MERAWProcessingFloatParameter",
        b"hasCameraValue:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"MERAWProcessingFloatParameter",
        b"hasNeutralValue:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"MERAWProcessingIntegerParameter",
        b"hasCameraValue:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"MERAWProcessingIntegerParameter",
        b"hasNeutralValue:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"MERAWProcessingListParameter",
        b"hasCameraValue:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"MERAWProcessingListParameter",
        b"hasNeutralValue:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(b"MERAWProcessingParameter", b"enabled", {"retval": {"type": b"Z"}})
    r(b"MERAWProcessingParameter", b"setEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"MERAWProcessorPixelBufferManager",
        b"createPixelBufferAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"MESampleCursorChunk",
        b"chunkInfo",
        {"retval": {"type": b"{AVSampleCursorChunkInfo=qZZZ}"}},
    )
    r(
        b"MESampleCursorChunk",
        b"initWithByteSource:chunkStorageRange:chunkInfo:sampleIndexWithinChunk:",
        {"arguments": {4: {"type": b"{AVSampleCursorChunkInfo=qZZZ}"}}},
    )
    r(b"METrackInfo", b"isEnabled", {"retval": {"type": b"Z"}})
    r(b"METrackInfo", b"requiresFrameReordering", {"retval": {"type": b"Z"}})
    r(b"METrackInfo", b"setEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"METrackInfo",
        b"setRequiresFrameReordering:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MEVideoDecoderPixelBufferManager",
        b"createPixelBufferAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(b"NSObject", b"actualThreadCount", {"required": False, "retval": {"type": b"q"}})
    r(
        b"NSObject",
        b"canAcceptFormatDescription:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"^{opaqueCMFormatDescription=}"}},
        },
    )
    r(
        b"NSObject",
        b"chunkDetailsReturningError:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"contentHasInterframeDependencies",
        {"required": False, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"currentSampleDuration",
        {"required": True, "retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"NSObject",
        b"currentSampleFormatDescription",
        {"required": True, "retval": {"type": b"^{opaqueCMFormatDescription=}"}},
    )
    r(
        b"NSObject",
        b"decodeFrameFromSampleBuffer:options:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^{opaqueCMSampleBuffer=}"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^{__CVBuffer=}"},
                            2: {"type": b"Q"},
                            3: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"decodeTimeOfLastSampleReachableByForwardSteppingThatIsAlreadyLoadedByByteSource",
        {"required": False, "retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"NSObject",
        b"decodeTimeStamp",
        {"required": True, "retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"NSObject",
        b"dependencyInfo",
        {
            "required": False,
            "retval": {"type": b"{AVSampleCursorDependencyInfo=ZZZZZZ}"},
        },
    )
    r(
        b"NSObject",
        b"estimatedSampleLocationReturningError:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"formatReaderWithByteSource:options:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"^@", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"generateSampleCursorAtFirstSampleInDecodeOrderWithCompletionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"generateSampleCursorAtLastSampleInDecodeOrderWithCompletionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"generateSampleCursorAtPresentationTimeStamp:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(b"NSObject", b"hevcDependencyInfo", {"required": False, "retval": {"type": b"@"}})
    r(b"NSObject", b"init", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"isReadyForMoreMediaData",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"loadEstimatedDataRateWithCompletionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"f"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"loadFileInfoWithCompletionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"loadMetadataWithCompletionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"loadPostDecodeProcessingMetadataWithCompletionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"loadSampleBufferContainingSamplesToEndCursor:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^{opaqueCMSampleBuffer=}"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"loadTotalSampleDataLengthWithCompletionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"loadTrackInfoWithCompletionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"loadTrackReadersWithCompletionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"loadUneditedDurationWithCompletionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"metalDeviceRegistryID",
        {"required": False, "retval": {"type": b"Q"}},
    )
    r(
        b"NSObject",
        b"outputColorAttachments",
        {"required": False, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"parseAdditionalFragmentsWithCompletionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"pixelFormatsWithReducedResolutionDecodeSupport",
        {"required": False, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"presentationTimeStamp",
        {"required": True, "retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"NSObject",
        b"processFrameFromImageBuffer:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^{__CVBuffer=}"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^{__CVBuffer=}"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"processingParameters",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"processorWithFormatDescription:extensionPixelBufferManager:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"^{opaqueCMFormatDescription=}"},
                3: {"type": b"@"},
                4: {"type": b"^@", "type_modifier": b"o"},
            },
        },
    )
    r(b"NSObject", b"producesRAWOutput", {"required": False, "retval": {"type": b"Z"}})
    r(
        b"NSObject",
        b"recommendedThreadCount",
        {"required": False, "retval": {"type": b"q"}},
    )
    r(
        b"NSObject",
        b"reducedResolution",
        {"required": False, "retval": {"type": b"{CGSize=dd}"}},
    )
    r(
        b"NSObject",
        b"refineSampleLocation:refinementData:refinementDataLength:refinedLocation:error:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type": b"{AVSampleCursorStorageRange=qq}"},
                3: {"type": "^v", "type_modifier": b"n", "c_array_length_in_arg": 4},
                4: {"type": b"Q"},
                5: {"type": b"^{AVSampleCursorStorageRange=qq}", "type_modifier": b"o"},
                6: {"type": b"^@", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"sampleLocationReturningError:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"samplesWithEarlierDTSsMayHaveLaterPTSsThanCursor:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"samplesWithLaterDTSsMayHaveEarlierPTSsThanCursor:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setMetalDeviceRegistryID:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"setRecommendedThreadCount:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"q"}}},
    )
    r(
        b"NSObject",
        b"setReducedResolution:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"{CGSize=dd}"}},
        },
    )
    r(
        b"NSObject",
        b"stepByDecodeTime:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                            2: {"type": b"Z"},
                            3: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"stepByPresentationTime:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                            2: {"type": b"Z"},
                            3: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"stepInDecodeOrderByCount:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"q"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"stepInPresentationOrderByCount:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"q"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"supportedPixelFormatsOrderedByQuality",
        {"required": False, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"syncInfo",
        {"required": False, "retval": {"type": b"{AVSampleCursorSyncInfo=ZZZ}"}},
    )
    r(
        b"NSObject",
        b"videoDecoderWithCodecType:videoFormatDescription:videoDecoderSpecifications:extensionDecoderPixelBufferManager:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"I"},
                3: {"type": b"^{opaqueCMFormatDescription=}"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {"type": b"^@", "type_modifier": b"o"},
            },
        },
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "MEEstimatedSampleLocation",
    b"initWithByteSource:estimatedSampleLocation:refinementDataLocation:",
)
objc.registerNewKeywordsFromSelector(
    "MERAWProcessingBooleanParameter", b"initWithName:key:description:initialValue:"
)
objc.registerNewKeywordsFromSelector(
    "MERAWProcessingBooleanParameter",
    b"initWithName:key:description:initialValue:cameraValue:",
)
objc.registerNewKeywordsFromSelector(
    "MERAWProcessingBooleanParameter",
    b"initWithName:key:description:initialValue:neutralValue:",
)
objc.registerNewKeywordsFromSelector(
    "MERAWProcessingBooleanParameter",
    b"initWithName:key:description:initialValue:neutralValue:cameraValue:",
)
objc.registerNewKeywordsFromSelector(
    "MERAWProcessingFloatParameter",
    b"initWithName:key:description:initialValue:maximum:minimum:",
)
objc.registerNewKeywordsFromSelector(
    "MERAWProcessingFloatParameter",
    b"initWithName:key:description:initialValue:maximum:minimum:cameraValue:",
)
objc.registerNewKeywordsFromSelector(
    "MERAWProcessingFloatParameter",
    b"initWithName:key:description:initialValue:maximum:minimum:neutralValue:",
)
objc.registerNewKeywordsFromSelector(
    "MERAWProcessingFloatParameter",
    b"initWithName:key:description:initialValue:maximum:minimum:neutralValue:cameraValue:",
)
objc.registerNewKeywordsFromSelector(
    "MERAWProcessingIntegerParameter",
    b"initWithName:key:description:initialValue:maximum:minimum:",
)
objc.registerNewKeywordsFromSelector(
    "MERAWProcessingIntegerParameter",
    b"initWithName:key:description:initialValue:maximum:minimum:cameraValue:",
)
objc.registerNewKeywordsFromSelector(
    "MERAWProcessingIntegerParameter",
    b"initWithName:key:description:initialValue:maximum:minimum:neutralValue:",
)
objc.registerNewKeywordsFromSelector(
    "MERAWProcessingIntegerParameter",
    b"initWithName:key:description:initialValue:maximum:minimum:neutralValue:cameraValue:",
)
objc.registerNewKeywordsFromSelector(
    "MERAWProcessingListElementParameter", b"initWithName:description:elementID:"
)
objc.registerNewKeywordsFromSelector(
    "MERAWProcessingListParameter", b"initWithName:key:description:list:initialValue:"
)
objc.registerNewKeywordsFromSelector(
    "MERAWProcessingListParameter",
    b"initWithName:key:description:list:initialValue:cameraValue:",
)
objc.registerNewKeywordsFromSelector(
    "MERAWProcessingListParameter",
    b"initWithName:key:description:list:initialValue:neutralValue:",
)
objc.registerNewKeywordsFromSelector(
    "MERAWProcessingListParameter",
    b"initWithName:key:description:list:initialValue:neutralValue:cameraValue:",
)
objc.registerNewKeywordsFromSelector(
    "MERAWProcessingParameterListElement", b"initWithName:description:elementID:"
)
objc.registerNewKeywordsFromSelector(
    "MERAWProcessingSubGroupParameter", b"initWithName:description:parameters:"
)
objc.registerNewKeywordsFromSelector(
    "MESampleCursorChunk",
    b"initWithByteSource:chunkStorageRange:chunkInfo:sampleIndexWithinChunk:",
)
objc.registerNewKeywordsFromSelector(
    "MESampleLocation", b"initWithByteSource:sampleLocation:"
)
objc.registerNewKeywordsFromSelector(
    "METrackInfo", b"initWithMediaType:trackID:formatDescriptions:"
)
expressions = {}

# END OF FILE
