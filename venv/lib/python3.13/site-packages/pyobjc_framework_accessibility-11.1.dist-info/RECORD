Accessibility/_Accessibility.cpython-313-darwin.so,sha256=ZHNfQxXjBxPQZTqmC-nC2n9OikacBBNvIBcuDVC18xE,69816
Accessibility/__init__.py,sha256=UByHOosBUXJGGNLRIhO5WP0YjI0cwYbin4sg7leL_S0,2144
Accessibility/__pycache__/__init__.cpython-313.pyc,,
Accessibility/__pycache__/_metadata.cpython-313.pyc,,
Accessibility/_metadata.py,sha256=UPMSXnIfOmhsfygMAklaRzT4QVVFeKCk12AnO2ieOBM,11832
pyobjc_framework_accessibility-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_accessibility-11.1.dist-info/METADATA,sha256=qviVkQ247K0GRIkAG7OAWet3IIjbm9wXTTjbt4I3_tI,2488
pyobjc_framework_accessibility-11.1.dist-info/RECORD,,
pyobjc_framework_accessibility-11.1.dist-info/WHEEL,sha256=memlX0NSEQnmSMa3rcNWPnk4cttudwgAZx3qq8qO4ME,115
pyobjc_framework_accessibility-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_accessibility-11.1.dist-info/top_level.txt,sha256=W3LULf_eBzuol6BjP1T0HKCU9XHVoKXuWn1WVdwIUGk,14
