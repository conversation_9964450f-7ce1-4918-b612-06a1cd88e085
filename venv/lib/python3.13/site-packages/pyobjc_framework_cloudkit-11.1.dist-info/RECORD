CloudKit/__init__.py,sha256=aOJWU54hrBjEXQgm9BhePUF43pKNcs54CXYwM6CccoY,6271
CloudKit/__pycache__/__init__.cpython-313.pyc,,
CloudKit/__pycache__/_metadata.cpython-313.pyc,,
CloudKit/_metadata.py,sha256=veWRh3498dnEKl3ia-XVgL-z7otaG5qbD07O7lIKXf4,96085
pyobjc_framework_cloudkit-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_cloudkit-11.1.dist-info/METADATA,sha256=cvcT4CEmXGm9f4IwgD6TPalJEQBzXJtdCOukNe-CdfI,2617
pyobjc_framework_cloudkit-11.1.dist-info/RECORD,,
pyobjc_framework_cloudkit-11.1.dist-info/WHEEL,sha256=JNWh1Fm1UdwIQV075glCn4MVuCRs0sotJIq-J6rbxCU,109
pyobjc_framework_cloudkit-11.1.dist-info/licenses/LICENSE.txt,sha256=DKBLB5KNSHK52bsiGHygQm3Yv6sI8m6toJmacdyBqv8,1249
pyobjc_framework_cloudkit-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_cloudkit-11.1.dist-info/top_level.txt,sha256=z7XD0iXYs36v7_IFOgKCe9zhkmRayocyyxmErwD6ZwQ,9
