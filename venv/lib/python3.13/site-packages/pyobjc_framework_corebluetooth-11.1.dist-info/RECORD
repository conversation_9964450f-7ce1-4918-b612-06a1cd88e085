CoreBluetooth/_CoreBluetooth.cpython-313-darwin.so,sha256=6m_jZ1xPdWgZWBIitJC5heyDIxn6-5dc1zM0ZKiRbpQ,85448
CoreBluetooth/__init__.py,sha256=f_cqazfU40pYz7ptMq0jZaWQNMnfgXps_w-4-0IIjvc,1130
CoreBluetooth/__pycache__/__init__.cpython-313.pyc,,
CoreBluetooth/__pycache__/_metadata.cpython-313.pyc,,
CoreBluetooth/_metadata.py,sha256=CqYtANVUBQRBdoqb8_ua-Ug33Si7wglAWLPHGxGt0BU,19594
pyobjc_framework_corebluetooth-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_corebluetooth-11.1.dist-info/METADATA,sha256=8lzOFQiBHXRenshng7ewBHLSW9oosT5vzEdCLXlM93s,2492
pyobjc_framework_corebluetooth-11.1.dist-info/RECORD,,
pyobjc_framework_corebluetooth-11.1.dist-info/WHEEL,sha256=memlX0NSEQnmSMa3rcNWPnk4cttudwgAZx3qq8qO4ME,115
pyobjc_framework_corebluetooth-11.1.dist-info/licenses/LICENSE.txt,sha256=DKBLB5KNSHK52bsiGHygQm3Yv6sI8m6toJmacdyBqv8,1249
pyobjc_framework_corebluetooth-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_corebluetooth-11.1.dist-info/top_level.txt,sha256=mrd_Sh1JLPW4g_20hfpKNneNXo-XHeaIG4n0zAvaIP0,14
