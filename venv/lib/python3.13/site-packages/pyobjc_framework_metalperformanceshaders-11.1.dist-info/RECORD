MetalPerformanceShaders/_MetalPerformanceShaders.cpython-313-darwin.so,sha256=AMA0HG-9dDy3GMzLmM9_ctlmqF3_tnXXidXNiLunDEo,89664
MetalPerformanceShaders/__init__.py,sha256=Hs-PQerJwRX3bo0V1onqHviqDlgWJ8AiYB2ScNXMgaE,12418
MetalPerformanceShaders/__pycache__/__init__.cpython-313.pyc,,
MetalPerformanceShaders/__pycache__/_metadata.cpython-313.pyc,,
MetalPerformanceShaders/_inlines.cpython-313-darwin.so,sha256=RaoBvVIrmM-jvCe1nlZ0ccPCRY7tuhZfQgdyza0KCRo,68000
MetalPerformanceShaders/_metadata.py,sha256=Mj5cNn1AEOPmLThqjt45FXAPYAGYXVbBrK58fdDoAKU,155034
pyobjc_framework_metalperformanceshaders-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_metalperformanceshaders-11.1.dist-info/METADATA,sha256=HkbNzFY-au5kUcOIsPJSAf1QjDMQXMp0uo5muV_kJR8,2484
pyobjc_framework_metalperformanceshaders-11.1.dist-info/RECORD,,
pyobjc_framework_metalperformanceshaders-11.1.dist-info/WHEEL,sha256=memlX0NSEQnmSMa3rcNWPnk4cttudwgAZx3qq8qO4ME,115
pyobjc_framework_metalperformanceshaders-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_metalperformanceshaders-11.1.dist-info/top_level.txt,sha256=eKE1aY5RA59TKaC_yls-rTlnlKb59ubFJop1wm1yTng,24
