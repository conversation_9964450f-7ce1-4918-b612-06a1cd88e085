LinkPresentation/__init__.py,sha256=2nlMCxn-24mttfDuWpMlMcz_Xl-X2y0kv2qMJHEMdy0,1042
LinkPresentation/__pycache__/__init__.cpython-313.pyc,,
LinkPresentation/__pycache__/_metadata.cpython-313.pyc,,
LinkPresentation/_metadata.py,sha256=0Ny80CjUjvMlINLvj3vRK-qHv0D8xJSh9E_Ew4UPtzo,2290
pyobjc_framework_linkpresentation-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_linkpresentation-11.1.dist-info/METADATA,sha256=j__PU3b_mkKXEJt9BlTpHpLEMn5NVly8AKSjmEZEer4,2501
pyobjc_framework_linkpresentation-11.1.dist-info/RECORD,,
pyobjc_framework_linkpresentation-11.1.dist-info/WHEEL,sha256=JNWh1Fm1UdwIQV075glCn4MVuCRs0sotJIq-J6rbxCU,109
pyobjc_framework_linkpresentation-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_linkpresentation-11.1.dist-info/top_level.txt,sha256=yWPJbzlhsNxrWF8DVHiXqahOP-NVOn-IFpzw5W2Ijmw,17
