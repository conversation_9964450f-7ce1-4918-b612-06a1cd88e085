OSLog/_OSLog.cpython-313-darwin.so,sha256=C6YTv7o3yBGH0dJw9-Ktc8KTs6Al0FuWlw9OAHeAQAI,67968
OSLog/__init__.py,sha256=xhsKqyXpDQ4ua85WjssZeCTG6nlbIIWM0LEe-IbTeSo,971
OSLog/__pycache__/__init__.cpython-313.pyc,,
OSLog/__pycache__/_metadata.cpython-313.pyc,,
OSLog/_metadata.py,sha256=5HaMuCKbVQ4ohqJ7RhOJVt3hbLo_voNDbww548UeTbw,3483
pyobjc_framework_oslog-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_oslog-11.1.dist-info/METADATA,sha256=LfKUznitYqZON1ojrKt3mRLtOcUz0UsyLr2RucXO0uA,2505
pyobjc_framework_oslog-11.1.dist-info/RECORD,,
pyobjc_framework_oslog-11.1.dist-info/WHEEL,sha256=memlX0NSEQnmSMa3rcNWPnk4cttudwgAZx3qq8qO4ME,115
pyobjc_framework_oslog-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_oslog-11.1.dist-info/top_level.txt,sha256=suGr-k3JNTOBjhpZrvQq0jnIr9Xgw6dZ5xnvN7HXkj8,6
