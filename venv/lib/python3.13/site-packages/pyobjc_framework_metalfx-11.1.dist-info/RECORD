MetalFX/_MetalFX.cpython-313-darwin.so,sha256=UP4VOi02Wt2dy9J1_G28iHxg3SVEN9YGxnG3M-jyCLU,84864
MetalFX/__init__.py,sha256=UobbQj9274xgtMRaOu6WJMZ7QP4AnAG0pOiNta8SnrY,874
MetalFX/__pycache__/__init__.cpython-313.pyc,,
MetalFX/__pycache__/_metadata.cpython-313.pyc,,
MetalFX/_metadata.py,sha256=q12KmQ0CoSyq_8q4AuRLQcFtbqGxlnxLTO664aR1wcc,7591
pyobjc_framework_metalfx-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_metalfx-11.1.dist-info/METADATA,sha256=7k2S1sEOYn-fowlg1l1D-N90OIwM-HnOU5isHz7A2oM,2419
pyobjc_framework_metalfx-11.1.dist-info/RECORD,,
pyobjc_framework_metalfx-11.1.dist-info/WHEEL,sha256=memlX0NSEQnmSMa3rcNWPnk4cttudwgAZx3qq8qO4ME,115
pyobjc_framework_metalfx-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_metalfx-11.1.dist-info/top_level.txt,sha256=LVGn6NujDh_YlQlhwAJAfVpSgyS_mVmAzEBBPgduHIE,8
