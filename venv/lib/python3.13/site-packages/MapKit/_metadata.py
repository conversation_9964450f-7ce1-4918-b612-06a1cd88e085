# This file is generated by objective.metadata
#
# Last update: Tue <PERSON> 11 10:14:06 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
misc.update(
    {
        "MKMapRect": objc.createStructType(
            "MapKit.MKMapRect",
            b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",
            ["origin", "size"],
        ),
        "MKMapSize": objc.createStructType(
            "MapKit.MKMapSize", b"{MKMapSize=dd}", ["width", "height"]
        ),
        "MKCoordinateRegion": objc.createStructType(
            "MapKit.MKCoordinateRegion",
            b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}",
            ["center", "span"],
        ),
        "MKCoordinateSpan": objc.createStructType(
            "MapKit.MKCoordinateSpan",
            b"{MKCoordinateSpan=dd}",
            ["latitudeDelta", "longitudeDelta"],
        ),
        "MKTileOverlayPath": objc.createStructType(
            "MapKit.MKTileOverlayPath",
            b"{MKTileOverlayPath=qqqd}",
            ["x", "y", "z", "contentScaleFactor"],
        ),
        "MKMapPoint": objc.createStructType(
            "MapKit.MKMapPoint", b"{MKMapPoint=dd}", ["x", "y"]
        ),
    }
)
constants = """$MKAnnotationCalloutInfoDidChangeNotification$MKErrorDomain$MKLaunchOptionsCameraKey$MKLaunchOptionsDirectionsModeDefault$MKLaunchOptionsDirectionsModeDriving$MKLaunchOptionsDirectionsModeKey$MKLaunchOptionsDirectionsModeTransit$MKLaunchOptionsDirectionsModeWalking$MKLaunchOptionsMapCenterKey$MKLaunchOptionsMapSpanKey$MKLaunchOptionsMapTypeKey$MKLaunchOptionsShowsTrafficKey$MKMapCameraZoomDefault@d$MKMapItemTypeIdentifier$MKMapRectNull@{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}$MKMapRectWorld@{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}$MKMapSizeWorld@{MKMapSize=dd}$MKMapViewDefaultAnnotationViewReuseIdentifier$MKMapViewDefaultClusterAnnotationViewReuseIdentifier$MKPointOfInterestCategoryATM$MKPointOfInterestCategoryAirport$MKPointOfInterestCategoryAmusementPark$MKPointOfInterestCategoryAnimalService$MKPointOfInterestCategoryAquarium$MKPointOfInterestCategoryAutomotiveRepair$MKPointOfInterestCategoryBakery$MKPointOfInterestCategoryBank$MKPointOfInterestCategoryBaseball$MKPointOfInterestCategoryBasketball$MKPointOfInterestCategoryBeach$MKPointOfInterestCategoryBeauty$MKPointOfInterestCategoryBowling$MKPointOfInterestCategoryBrewery$MKPointOfInterestCategoryCafe$MKPointOfInterestCategoryCampground$MKPointOfInterestCategoryCarRental$MKPointOfInterestCategoryCastle$MKPointOfInterestCategoryConventionCenter$MKPointOfInterestCategoryDistillery$MKPointOfInterestCategoryEVCharger$MKPointOfInterestCategoryFairground$MKPointOfInterestCategoryFireStation$MKPointOfInterestCategoryFishing$MKPointOfInterestCategoryFitnessCenter$MKPointOfInterestCategoryFoodMarket$MKPointOfInterestCategoryFortress$MKPointOfInterestCategoryGasStation$MKPointOfInterestCategoryGoKart$MKPointOfInterestCategoryGolf$MKPointOfInterestCategoryHiking$MKPointOfInterestCategoryHospital$MKPointOfInterestCategoryHotel$MKPointOfInterestCategoryKayaking$MKPointOfInterestCategoryLandmark$MKPointOfInterestCategoryLaundry$MKPointOfInterestCategoryLibrary$MKPointOfInterestCategoryMailbox$MKPointOfInterestCategoryMarina$MKPointOfInterestCategoryMiniGolf$MKPointOfInterestCategoryMovieTheater$MKPointOfInterestCategoryMuseum$MKPointOfInterestCategoryMusicVenue$MKPointOfInterestCategoryNationalMonument$MKPointOfInterestCategoryNationalPark$MKPointOfInterestCategoryNightlife$MKPointOfInterestCategoryPark$MKPointOfInterestCategoryParking$MKPointOfInterestCategoryPharmacy$MKPointOfInterestCategoryPlanetarium$MKPointOfInterestCategoryPolice$MKPointOfInterestCategoryPostOffice$MKPointOfInterestCategoryPublicTransport$MKPointOfInterestCategoryRVPark$MKPointOfInterestCategoryRestaurant$MKPointOfInterestCategoryRestroom$MKPointOfInterestCategoryRockClimbing$MKPointOfInterestCategorySchool$MKPointOfInterestCategorySkatePark$MKPointOfInterestCategorySkating$MKPointOfInterestCategorySkiing$MKPointOfInterestCategorySoccer$MKPointOfInterestCategorySpa$MKPointOfInterestCategoryStadium$MKPointOfInterestCategoryStore$MKPointOfInterestCategorySurfing$MKPointOfInterestCategorySwimming$MKPointOfInterestCategoryTennis$MKPointOfInterestCategoryTheater$MKPointOfInterestCategoryUniversity$MKPointOfInterestCategoryVolleyball$MKPointOfInterestCategoryWinery$MKPointOfInterestCategoryZoo$MKPointsOfInterestRequestMaxRadius@d$"""
enums = """$MKAddressFilterOptionAdministrativeArea@2$MKAddressFilterOptionCountry@1$MKAddressFilterOptionLocality@8$MKAddressFilterOptionPostalCode@32$MKAddressFilterOptionSubAdministrativeArea@4$MKAddressFilterOptionSubLocality@16$MKAnnotationViewCollisionModeCircle@1$MKAnnotationViewCollisionModeNone@2$MKAnnotationViewCollisionModeRectangle@0$MKAnnotationViewDragStateCanceling@3$MKAnnotationViewDragStateDragging@2$MKAnnotationViewDragStateEnding@4$MKAnnotationViewDragStateNone@0$MKAnnotationViewDragStateStarting@1$MKAnnotationViewZPriorityDefaultSelected@1000.0$MKAnnotationViewZPriorityDefaultUnselected@500.0$MKAnnotationViewZPriorityMax@1000.0$MKAnnotationViewZPriorityMin@0.0$MKDirectionsRoutePreferenceAny@0$MKDirectionsRoutePreferenceAvoid@1$MKDirectionsTransportTypeAny@268435455$MKDirectionsTransportTypeAutomobile@1$MKDirectionsTransportTypeTransit@4$MKDirectionsTransportTypeWalking@2$MKDistanceFormatterUnitStyleAbbreviated@1$MKDistanceFormatterUnitStyleDefault@0$MKDistanceFormatterUnitStyleFull@2$MKDistanceFormatterUnitsDefault@0$MKDistanceFormatterUnitsImperial@2$MKDistanceFormatterUnitsImperialWithYards@3$MKDistanceFormatterUnitsMetric@1$MKErrorDecodingFailed@6$MKErrorDirectionsNotFound@5$MKErrorLoadingThrottled@3$MKErrorPlacemarkNotFound@4$MKErrorServerFailure@2$MKErrorUnknown@1$MKFeatureDisplayPriorityDefaultHigh@750$MKFeatureDisplayPriorityDefaultLow@250$MKFeatureDisplayPriorityRequired@1000$MKFeatureVisibilityAdaptive@0$MKFeatureVisibilityHidden@1$MKFeatureVisibilityVisible@2$MKLocalSearchCompleterResultTypeAddress@1$MKLocalSearchCompleterResultTypePhysicalFeature@8$MKLocalSearchCompleterResultTypePointOfInterest@2$MKLocalSearchCompleterResultTypeQuery@4$MKLocalSearchRegionPriorityDefault@0$MKLocalSearchRegionPriorityRequired@1$MKLocalSearchResultTypeAddress@1$MKLocalSearchResultTypePhysicalFeature@4$MKLocalSearchResultTypePointOfInterest@2$MKLookAroundBadgePositionBottomTrailing@2$MKLookAroundBadgePositionTopLeading@0$MKLookAroundBadgePositionTopTrailing@1$MKMapElevationStyleFlat@0$MKMapElevationStyleRealistic@1$MKMapItemDetailSelectionAccessoryCalloutStyleAutomatic@0$MKMapItemDetailSelectionAccessoryCalloutStyleCompact@2$MKMapItemDetailSelectionAccessoryCalloutStyleFull@1$MKMapTypeHybrid@2$MKMapTypeHybridFlyover@4$MKMapTypeMutedStandard@5$MKMapTypeSatellite@1$MKMapTypeSatelliteFlyover@3$MKMapTypeStandard@0$MKOverlayLevelAboveLabels@1$MKOverlayLevelAboveRoads@0$MKPinAnnotationColorGreen@1$MKPinAnnotationColorPurple@2$MKPinAnnotationColorRed@0$MKSearchCompletionFilterTypeLocationsAndQueries@0$MKSearchCompletionFilterTypeLocationsOnly@1$MKStandardMapEmphasisStyleDefault@0$MKStandardMapEmphasisStyleMuted@1$MKUserTrackingModeFollow@1$MKUserTrackingModeFollowWithHeading@2$MKUserTrackingModeNone@0$"""
misc.update(
    {
        "MKAnnotationViewCollisionMode": NewType("MKAnnotationViewCollisionMode", int),
        "MKDistanceFormatterUnitStyle": NewType("MKDistanceFormatterUnitStyle", int),
        "MKPinAnnotationColor": NewType("MKPinAnnotationColor", int),
        "MKLocalSearchResultType": NewType("MKLocalSearchResultType", int),
        "MKDirectionsTransportType": NewType("MKDirectionsTransportType", int),
        "MKLookAroundBadgePosition": NewType("MKLookAroundBadgePosition", int),
        "MKDistanceFormatterUnits": NewType("MKDistanceFormatterUnits", int),
        "MKErrorCode": NewType("MKErrorCode", int),
        "MKMapElevationStyle": NewType("MKMapElevationStyle", int),
        "MKOverlayLevel": NewType("MKOverlayLevel", int),
        "MKMapItemDetailSelectionAccessoryCalloutStyle": NewType(
            "MKMapItemDetailSelectionAccessoryCalloutStyle", int
        ),
        "MKSearchCompletionFilterType": NewType("MKSearchCompletionFilterType", int),
        "MKDirectionsRoutePreference": NewType("MKDirectionsRoutePreference", int),
        "MKAnnotationViewDragState": NewType("MKAnnotationViewDragState", int),
        "MKLocalSearchCompleterResultType": NewType(
            "MKLocalSearchCompleterResultType", int
        ),
        "MKStandardMapEmphasisStyle": NewType("MKStandardMapEmphasisStyle", int),
        "MKMapType": NewType("MKMapType", int),
        "MKAddressFilterOption": NewType("MKAddressFilterOption", int),
        "MKFeatureVisibility": NewType("MKFeatureVisibility", int),
        "MKLocalSearchRegionPriority": NewType("MKLocalSearchRegionPriority", int),
        "MKUserTrackingMode": NewType("MKUserTrackingMode", int),
    }
)
misc.update(
    {
        "MKFeatureDisplayPriority": NewType("MKFeatureDisplayPriority", float),
        "MKPointOfInterestCategory": NewType("MKPointOfInterestCategory", str),
        "MKAnnotationViewZPriority": NewType("MKAnnotationViewZPriority", float),
    }
)
misc.update({})
functions = {
    "MKMapRectOffset": (
        b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}dd",
    ),
    "MKMapRectIsEmpty": (b"Z{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",),
    "MKMapRectGetMidX": (b"d{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",),
    "MKMapRectGetMidY": (b"d{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",),
    "MKMapRectGetMinX": (b"d{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",),
    "MKMapRectGetWidth": (b"d{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",),
    "MKStringFromMapRect": (b"@{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",),
    "MKCoordinateSpanMake": (b"{MKCoordinateSpan=dd}dd",),
    "MKMapRectGetMaxX": (b"d{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",),
    "MKMapRectGetMaxY": (b"d{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",),
    "MKMapSizeEqualToSize": (b"Z{MKMapSize=dd}{MKMapSize=dd}",),
    "MKMapRectIsNull": (b"Z{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",),
    "MKStringFromMapPoint": (b"@{MKMapPoint=dd}",),
    "MKMapRectDivide": (
        b"v{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}^{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}^{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}dI",
        "",
        {"arguments": {1: {"type_modifier": "o"}, 2: {"type_modifier": "o"}}},
    ),
    "MKMetersPerMapPointAtLatitude": (b"dd",),
    "MKCoordinateRegionMakeWithDistance": (
        b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}{CLLocationCoordinate2D=dd}dd",
    ),
    "MKMapRectMake": (b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}dddd",),
    "MKMapPointEqualToPoint": (b"Z{MKMapPoint=dd}{MKMapPoint=dd}",),
    "MKMapRectContainsPoint": (
        b"Z{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}{MKMapPoint=dd}",
    ),
    "MKMapRectUnion": (
        b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",
    ),
    "MKCoordinateForMapPoint": (b"{CLLocationCoordinate2D=dd}{MKMapPoint=dd}",),
    "MKMapRectRemainder": (
        b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",
    ),
    "MKCoordinateRegionMake": (
        b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}{CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}",
    ),
    "MKStringFromMapSize": (b"@{MKMapSize=dd}",),
    "MKCoordinateRegionForMapRect": (
        b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",
    ),
    "MKMapSizeMake": (b"{MKMapSize=dd}dd",),
    "MKMapRectIntersection": (
        b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",
    ),
    "MKMapRectInset": (
        b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}dd",
    ),
    "MKMapRectContainsRect": (
        b"Z{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",
    ),
    "MKMapRectGetMinY": (b"d{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",),
    "MKRoadWidthAtZoomScale": (b"dd",),
    "MKMapRectSpans180thMeridian": (b"Z{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",),
    "MKMapPointForCoordinate": (b"{MKMapPoint=dd}{CLLocationCoordinate2D=dd}",),
    "MKMapPointsPerMeterAtLatitude": (b"dd",),
    "MKMapRectIntersectsRect": (
        b"Z{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",
    ),
    "MKMetersBetweenMapPoints": (b"d{MKMapPoint=dd}{MKMapPoint=dd}",),
    "MKMapPointMake": (b"{MKMapPoint=dd}dd",),
    "MKMapRectEqualToRect": (
        b"Z{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",
    ),
    "MKMapRectGetHeight": (b"d{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}",),
}
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(b"MKAddressFilter", b"excludesOptions:", {"retval": {"type": b"Z"}})
    r(b"MKAddressFilter", b"includesOptions:", {"retval": {"type": b"Z"}})
    r(b"MKAnnotationView", b"canShowCallout", {"retval": {"type": b"Z"}})
    r(b"MKAnnotationView", b"isDraggable", {"retval": {"type": b"Z"}})
    r(b"MKAnnotationView", b"isEnabled", {"retval": {"type": b"Z"}})
    r(b"MKAnnotationView", b"isHighlighted", {"retval": {"type": b"Z"}})
    r(b"MKAnnotationView", b"isSelected", {"retval": {"type": b"Z"}})
    r(b"MKAnnotationView", b"setCanShowCallout:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"MKAnnotationView",
        b"setDragState:animated:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(b"MKAnnotationView", b"setDraggable:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MKAnnotationView", b"setEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MKAnnotationView", b"setHighlighted:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MKAnnotationView", b"setSelected:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"MKAnnotationView",
        b"setSelected:animated:",
        {"arguments": {2: {"type": b"Z"}, 3: {"type": b"Z"}}},
    )
    r(
        b"MKCircle",
        b"boundingMapRect",
        {"retval": {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"}},
    )
    r(
        b"MKCircle",
        b"circleWithCenterCoordinate:radius:",
        {"arguments": {2: {"type": b"{CLLocationCoordinate2D=dd}"}}},
    )
    r(
        b"MKCircle",
        b"circleWithMapRect:",
        {"arguments": {2: {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"}}},
    )
    r(b"MKCircle", b"coordinate", {"retval": {"type": b"{CLLocationCoordinate2D=dd}"}})
    r(
        b"MKDirections",
        b"calculateDirectionsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"MKDirections",
        b"calculateETAWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"MKDirections", b"isCalculating", {"retval": {"type": b"Z"}})
    r(b"MKDirectionsRequest", b"isDirectionsRequestURL:", {"retval": {"type": b"Z"}})
    r(b"MKDirectionsRequest", b"requestsAlternateRoutes", {"retval": {"type": b"Z"}})
    r(
        b"MKDirectionsRequest",
        b"setRequestsAlternateRoutes:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MKGeoJSONDecoder",
        b"geoJSONObjectsWithData:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"MKGeodesicPolyline",
        b"polylineWithCoordinates:count:",
        {
            "arguments": {
                2: {
                    "type": b"^{CLLocationCoordinate2D=dd}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MKGeodesicPolyline",
        b"polylineWithPoints:count:",
        {
            "arguments": {
                2: {
                    "type": b"^{MKMapPoint=dd}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MKHybridMapConfiguration",
        b"setShowsTraffic:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"MKHybridMapConfiguration", b"showsTraffic", {"retval": {"type": b"Z"}})
    r(
        b"MKLocalPointsOfInterestRequest",
        b"initWithCoordinateRegion:",
        {
            "arguments": {
                2: {
                    "type": b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}"
                }
            }
        },
    )
    r(
        b"MKLocalPointsOfInterestRequest",
        b"region",
        {
            "retval": {
                "type": b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}"
            }
        },
    )
    r(b"MKLocalSearch", b"isSearching", {"retval": {"type": b"Z"}})
    r(
        b"MKLocalSearch",
        b"startWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"MKLocalSearchCompleter", b"isSearching", {"retval": {"type": "Z"}})
    r(
        b"MKLocalSearchCompleter",
        b"region",
        {
            "retval": {
                "type": b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}"
            }
        },
    )
    r(
        b"MKLocalSearchCompleter",
        b"setRegion:",
        {
            "arguments": {
                2: {
                    "type": b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}"
                }
            }
        },
    )
    r(
        b"MKLocalSearchRequest",
        b"initWithNaturalLanguageQuery:region:",
        {
            "arguments": {
                3: {
                    "type": b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}"
                }
            }
        },
    )
    r(
        b"MKLocalSearchRequest",
        b"region",
        {
            "retval": {
                "type": b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}"
            }
        },
    )
    r(
        b"MKLocalSearchRequest",
        b"setRegion:",
        {
            "arguments": {
                2: {
                    "type": b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}"
                }
            }
        },
    )
    r(
        b"MKLocalSearchResponse",
        b"boundingRegion",
        {
            "retval": {
                "type": b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}"
            }
        },
    )
    r(
        b"MKLookAroundSceneRequest",
        b"getSceneWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"MKLookAroundSceneRequest", b"isCancelled", {"retval": {"type": b"Z"}})
    r(b"MKLookAroundSceneRequest", b"isLoading", {"retval": {"type": b"Z"}})
    r(
        b"MKLookAroundSnapshotter",
        b"getSnapshotWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"MKLookAroundSnapshotter", b"isLoading", {"retval": {"type": b"Z"}})
    r(b"MKLookAroundViewController", b"isNavigationEnabled", {"retval": {"type": b"Z"}})
    r(
        b"MKLookAroundViewController",
        b"setNavigationEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MKLookAroundViewController",
        b"setShowsRoadLabels:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"MKLookAroundViewController", b"showsRoadLabels", {"retval": {"type": b"Z"}})
    r(
        b"MKMapCamera",
        b"cameraLookingAtCenterCoordinate:fromEyeCoordinate:eyeAltitude:",
        {
            "arguments": {
                2: {"type": b"{CLLocationCoordinate2D=dd}"},
                3: {"type": b"{CLLocationCoordinate2D=dd}"},
            }
        },
    )
    r(
        b"MKMapCamera",
        b"cameraLookingAtMapItem:forViewSize:allowPitch:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"MKMapCamera",
        b"centerCoordinate",
        {"retval": {"type": b"{CLLocationCoordinate2D=dd}"}},
    )
    r(
        b"MKMapCamera",
        b"setCenterCoordinate:",
        {"arguments": {2: {"type": b"{CLLocationCoordinate2D=dd}"}}},
    )
    r(
        b"MKMapCameraBoundary",
        b"initWithCoordinateRegion:",
        {
            "arguments": {
                2: {
                    "type": b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}"
                }
            }
        },
    )
    r(
        b"MKMapCameraBoundary",
        b"initWithMapRect:",
        {"arguments": {2: {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"}}},
    )
    r(
        b"MKMapCameraBoundary",
        b"mapRect",
        {"retval": {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"}},
    )
    r(
        b"MKMapCameraBoundary",
        b"region",
        {
            "retval": {
                "type": b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}"
            }
        },
    )
    r(b"MKMapItem", b"isCurrentLocation", {"retval": {"type": b"Z"}})
    r(b"MKMapItem", b"openInMapsWithLaunchOptions:", {"retval": {"type": b"Z"}})
    r(
        b"MKMapItem",
        b"openInMapsWithLaunchOptions:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(b"MKMapItem", b"openMapsWithItems:launchOptions:", {"retval": {"type": b"Z"}})
    r(
        b"MKMapItem",
        b"openMapsWithItems:launchOptions:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"MKMapItemDetailViewController",
        b"initWithMapItem:displaysMap:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"MKMapItemRequest",
        b"getMapItemWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"MKMapItemRequest", b"isCancelled", {"retval": {"type": b"Z"}})
    r(b"MKMapItemRequest", b"isLoading", {"retval": {"type": b"Z"}})
    r(
        b"MKMapSnapshot",
        b"pointForCoordinate:",
        {"arguments": {2: {"type": b"{CLLocationCoordinate2D=dd}"}}},
    )
    r(
        b"MKMapSnapshotOptions",
        b"mapRect",
        {"retval": {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"}},
    )
    r(
        b"MKMapSnapshotOptions",
        b"region",
        {
            "retval": {
                "type": b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}"
            }
        },
    )
    r(
        b"MKMapSnapshotOptions",
        b"setMapRect:",
        {"arguments": {2: {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"}}},
    )
    r(
        b"MKMapSnapshotOptions",
        b"setRegion:",
        {
            "arguments": {
                2: {
                    "type": b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}"
                }
            }
        },
    )
    r(
        b"MKMapSnapshotOptions",
        b"setShowsBuildings:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MKMapSnapshotOptions",
        b"setShowsPointsOfInterest:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"MKMapSnapshotOptions", b"showsBuildings", {"retval": {"type": b"Z"}})
    r(b"MKMapSnapshotOptions", b"showsPointsOfInterest", {"retval": {"type": b"Z"}})
    r(b"MKMapSnapshotter", b"isLoading", {"retval": {"type": b"Z"}})
    r(
        b"MKMapSnapshotter",
        b"startWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"MKMapSnapshotter",
        b"startWithQueue:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"MKMapView",
        b"annotationsInMapRect:",
        {"arguments": {2: {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"}}},
    )
    r(
        b"MKMapView",
        b"centerCoordinate",
        {"retval": {"type": b"{CLLocationCoordinate2D=dd}"}},
    )
    r(
        b"MKMapView",
        b"convertCoordinate:toPointToView:",
        {"arguments": {2: {"type": b"{CLLocationCoordinate2D=dd}"}}},
    )
    r(
        b"MKMapView",
        b"convertPoint:toCoordinateFromView:",
        {"retval": {"type": b"{CLLocationCoordinate2D=dd}"}},
    )
    r(
        b"MKMapView",
        b"convertRect:toRegionFromView:",
        {
            "retval": {
                "type": b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}"
            }
        },
    )
    r(
        b"MKMapView",
        b"convertRegion:toRectToView:",
        {
            "arguments": {
                2: {
                    "type": b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}"
                }
            }
        },
    )
    r(b"MKMapView", b"deselectAnnotation:animated:", {"arguments": {3: {"type": b"Z"}}})
    r(b"MKMapView", b"isPitchEnabled", {"retval": {"type": b"Z"}})
    r(b"MKMapView", b"isRotateEnabled", {"retval": {"type": b"Z"}})
    r(b"MKMapView", b"isScrollEnabled", {"retval": {"type": b"Z"}})
    r(b"MKMapView", b"isUserLocationVisible", {"retval": {"type": b"Z"}})
    r(b"MKMapView", b"isZoomEnabled", {"retval": {"type": b"Z"}})
    r(
        b"MKMapView",
        b"mapRectThatFits:",
        {
            "retval": {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"},
            "arguments": {2: {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"}},
        },
    )
    r(
        b"MKMapView",
        b"mapRectThatFits:edgePadding:",
        {
            "retval": {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"},
            "arguments": {
                2: {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"},
                3: {"type": b"{_NSEdgeInsets=dddd}"},
            },
        },
    )
    r(
        b"MKMapView",
        b"region",
        {
            "retval": {
                "type": b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}"
            }
        },
    )
    r(
        b"MKMapView",
        b"regionThatFits:",
        {
            "retval": {
                "type": b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}"
            },
            "arguments": {
                2: {
                    "type": b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}"
                }
            },
        },
    )
    r(b"MKMapView", b"selectAnnotation:animated:", {"arguments": {3: {"type": b"Z"}}})
    r(b"MKMapView", b"setCamera:animated:", {"arguments": {3: {"type": b"Z"}}})
    r(b"MKMapView", b"setCameraBoundary:animated:", {"arguments": {3: {"type": b"Z"}}})
    r(b"MKMapView", b"setCameraZoomRange:animated:", {"arguments": {3: {"type": b"Z"}}})
    r(
        b"MKMapView",
        b"setCenterCoordinate:",
        {"arguments": {2: {"type": b"{CLLocationCoordinate2D=dd}"}}},
    )
    r(
        b"MKMapView",
        b"setCenterCoordinate:animated:",
        {"arguments": {2: {"type": b"{CLLocationCoordinate2D=dd}"}, 3: {"type": b"Z"}}},
    )
    r(b"MKMapView", b"setPitchEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"MKMapView",
        b"setRegion:",
        {
            "arguments": {
                2: {
                    "type": b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}"
                }
            }
        },
    )
    r(
        b"MKMapView",
        b"setRegion:animated:",
        {
            "arguments": {
                2: {
                    "type": b"{MKCoordinateRegion={CLLocationCoordinate2D=dd}{MKCoordinateSpan=dd}}"
                },
                3: {"type": b"Z"},
            }
        },
    )
    r(b"MKMapView", b"setRotateEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MKMapView", b"setScrollEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MKMapView", b"setShowsBuildings:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MKMapView", b"setShowsCompass:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MKMapView", b"setShowsPitchControl:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MKMapView", b"setShowsPointsOfInterest:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MKMapView", b"setShowsScale:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MKMapView", b"setShowsTraffic:", {"arguments": {2: {"type": "Z"}}})
    r(b"MKMapView", b"setShowsUserLocation:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MKMapView", b"setShowsUserTrackingButton:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MKMapView", b"setShowsZoomControls:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"MKMapView",
        b"setUserTrackingMode:animated:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"MKMapView",
        b"setVisibleMapRect:",
        {"arguments": {2: {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"}}},
    )
    r(
        b"MKMapView",
        b"setVisibleMapRect:animated:",
        {
            "arguments": {
                2: {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"},
                3: {"type": b"Z"},
            }
        },
    )
    r(
        b"MKMapView",
        b"setVisibleMapRect:edgePadding:animated:",
        {
            "arguments": {
                2: {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"},
                3: {"type": b"{_NSEdgeInsets=dddd}"},
                4: {"type": b"Z"},
            }
        },
    )
    r(b"MKMapView", b"setZoomEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MKMapView", b"showAnnotations:animated:", {"arguments": {3: {"type": b"Z"}}})
    r(b"MKMapView", b"showsBuildings", {"retval": {"type": b"Z"}})
    r(b"MKMapView", b"showsCompass", {"retval": {"type": b"Z"}})
    r(b"MKMapView", b"showsPitchControl", {"retval": {"type": b"Z"}})
    r(b"MKMapView", b"showsPointsOfInterest", {"retval": {"type": b"Z"}})
    r(b"MKMapView", b"showsScale", {"retval": {"type": b"Z"}})
    r(b"MKMapView", b"showsTraffic", {"retval": {"type": "Z"}})
    r(b"MKMapView", b"showsUserLocation", {"retval": {"type": b"Z"}})
    r(b"MKMapView", b"showsUserTrackingButton", {"retval": {"type": b"Z"}})
    r(b"MKMapView", b"showsZoomControls", {"retval": {"type": b"Z"}})
    r(
        b"MKMapView",
        b"visibleMapRect",
        {"retval": {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"}},
    )
    r(b"MKMarkerAnnotationView", b"animatesWhenAdded", {"retval": {"type": b"Z"}})
    r(
        b"MKMarkerAnnotationView",
        b"setAnimatesWhenAdded:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MKMultiPoint",
        b"getCoordinates:range:",
        {
            "arguments": {
                2: {
                    "type": b"^{CLLocationCoordinate2D=dd}",
                    "type_modifier": b"o",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MKMultiPoint",
        b"points",
        {"retval": {"type": b"^{MKMapPoint=dd}", "c_array_of_variable_length": True}},
    )
    r(
        b"MKOverlayPathRenderer",
        b"setShouldRasterize:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"MKOverlayPathRenderer", b"shouldRasterize", {"retval": {"type": b"Z"}})
    r(
        b"MKOverlayRenderer",
        b"canDrawMapRect:zoomScale:",
        {
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"}},
        },
    )
    r(
        b"MKOverlayRenderer",
        b"drawMapRect:zoomScale:inContext:",
        {"arguments": {2: {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"}}},
    )
    r(
        b"MKOverlayRenderer",
        b"mapPointForPoint:",
        {"retval": {"type": b"{MKMapPoint=dd}"}},
    )
    r(
        b"MKOverlayRenderer",
        b"mapRectForRect:",
        {"retval": {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"}},
    )
    r(
        b"MKOverlayRenderer",
        b"pointForMapPoint:",
        {"arguments": {2: {"type": b"{MKMapPoint=dd}"}}},
    )
    r(
        b"MKOverlayRenderer",
        b"rectForMapRect:",
        {"arguments": {2: {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"}}},
    )
    r(
        b"MKOverlayRenderer",
        b"setNeedsDisplayInMapRect:",
        {"arguments": {2: {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"}}},
    )
    r(
        b"MKOverlayRenderer",
        b"setNeedsDisplayInMapRect:zoomScale:",
        {"arguments": {2: {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"}}},
    )
    r(b"MKPinAnnotationView", b"animatesDrop", {"retval": {"type": b"Z"}})
    r(b"MKPinAnnotationView", b"setAnimatesDrop:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"MKPlacemark",
        b"initWithCoordinate:addressDictionary:",
        {"arguments": {2: {"type": b"{CLLocationCoordinate2D=dd}"}}},
    )
    r(
        b"MKPointAnnotation",
        b"coordinate",
        {"retval": {"type": b"{CLLocationCoordinate2D=dd}"}},
    )
    r(
        b"MKPointAnnotation",
        b"setCoordinate:",
        {"arguments": {2: {"type": b"{CLLocationCoordinate2D=dd}"}}},
    )
    r(b"MKPointOfInterestFilter", b"excludesCategory:", {"retval": {"type": b"Z"}})
    r(b"MKPointOfInterestFilter", b"includesCategory:", {"retval": {"type": b"Z"}})
    r(
        b"MKPolygon",
        b"polygonWithCoordinates:count:",
        {
            "arguments": {
                2: {
                    "type": b"^{CLLocationCoordinate2D=dd}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MKPolygon",
        b"polygonWithCoordinates:count:interiorPolygons:",
        {
            "arguments": {
                2: {
                    "type": b"^{CLLocationCoordinate2D=dd}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MKPolygon",
        b"polygonWithPoints:count:",
        {
            "arguments": {
                2: {
                    "type": b"^{MKMapPoint=dd}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MKPolygon",
        b"polygonWithPoints:count:interiorPolygons:",
        {
            "arguments": {
                2: {
                    "type": b"^{MKMapPoint=dd}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MKPolyline",
        b"polylineWithCoordinates:count:",
        {
            "arguments": {
                2: {
                    "type": b"^{CLLocationCoordinate2D=dd}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MKPolyline",
        b"polylineWithPoints:count:",
        {
            "arguments": {
                2: {
                    "type": b"^{MKMapPoint=dd}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(b"MKRoute", b"hasHighways", {"retval": {"type": b"Z"}})
    r(b"MKRoute", b"hasTolls", {"retval": {"type": b"Z"}})
    r(
        b"MKStandardMapConfiguration",
        b"setShowsTraffic:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"MKStandardMapConfiguration", b"showsTraffic", {"retval": {"type": b"Z"}})
    r(
        b"MKTileOverlay",
        b"URLForTilePath:",
        {"arguments": {2: {"type": b"{MKTileOverlayPath=qqqd}"}}},
    )
    r(b"MKTileOverlay", b"canReplaceMapContent", {"retval": {"type": b"Z"}})
    r(b"MKTileOverlay", b"isGeometryFlipped", {"retval": {"type": b"Z"}})
    r(
        b"MKTileOverlay",
        b"loadTileAtPath:result:",
        {
            "arguments": {
                2: {"type": b"{MKTileOverlayPath=qqqd}"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(b"MKTileOverlay", b"setCanReplaceMapContent:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MKTileOverlay", b"setGeometryFlipped:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MKUserLocation", b"isUpdating", {"retval": {"type": b"Z"}})
    r(
        b"NSObject",
        b"boundingMapRect",
        {
            "required": True,
            "retval": {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"},
        },
    )
    r(
        b"NSObject",
        b"canReplaceMapContent",
        {"required": False, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"completer:didFailWithError:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"completerDidUpdateResults:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"coordinate",
        {"required": True, "retval": {"type": b"{CLLocationCoordinate2D=dd}"}},
    )
    r(
        b"NSObject",
        b"intersectsMapRect:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"{MKMapRect={MKMapPoint=dd}{MKMapSize=dd}}"}},
        },
    )
    r(
        b"NSObject",
        b"lookAroundViewControllerDidDismissFullScreen:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"lookAroundViewControllerDidPresentFullScreen:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"lookAroundViewControllerDidUpdateScene:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"lookAroundViewControllerWillDismissFullScreen:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"lookAroundViewControllerWillPresentFullScreen:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"lookAroundViewControllerWillUpdateScene:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"mapItemDetailViewControllerDidFinish:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"mapView:annotationView:didChangeDragState:fromOldState:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"Q"},
                5: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"mapView:clusterAnnotationForMemberAnnotations:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"mapView:didAddAnnotationViews:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"mapView:didAddOverlayRenderers:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"mapView:didChangeUserTrackingMode:animated:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"q"}, 4: {"type": b"Z"}},
        },
    )
    r(
        b"NSObject",
        b"mapView:didDeselectAnnotation:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"mapView:didDeselectAnnotationView:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"mapView:didFailToLocateUserWithError:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"mapView:didSelectAnnotation:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"mapView:didSelectAnnotationView:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"mapView:didUpdateUserLocation:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"mapView:regionDidChangeAnimated:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Z"}},
        },
    )
    r(
        b"NSObject",
        b"mapView:regionWillChangeAnimated:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Z"}},
        },
    )
    r(
        b"NSObject",
        b"mapView:rendererForOverlay:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"mapView:selectionAccessoryForAnnotation:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"mapView:viewForAnnotation:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"mapViewDidChangeVisibleRegion:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"mapViewDidFailLoadingMap:withError:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"mapViewDidFinishLoadingMap:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"mapViewDidFinishRenderingMap:fullyRendered:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Z"}},
        },
    )
    r(
        b"NSObject",
        b"mapViewDidStopLocatingUser:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"mapViewWillStartLoadingMap:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"mapViewWillStartLocatingUser:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"mapViewWillStartRenderingMap:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setCoordinate:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"{CLLocationCoordinate2D=dd}"}},
        },
    )
    r(b"NSObject", b"subtitle", {"required": False, "retval": {"type": b"@"}})
    r(b"NSObject", b"title", {"required": False, "retval": {"type": b"@"}})
    r(
        b"NSValue",
        b"MKCoordinateSpanValue",
        {"retval": {"type": b"{MKCoordinateSpan=dd}"}},
    )
    r(
        b"NSValue",
        b"MKCoordinateValue",
        {"retval": {"type": b"{CLLocationCoordinate2D=dd}"}},
    )
    r(
        b"NSValue",
        b"valueWithMKCoordinate:",
        {"arguments": {2: {"type": b"{CLLocationCoordinate2D=dd}"}}},
    )
    r(
        b"NSValue",
        b"valueWithMKCoordinateSpan:",
        {"arguments": {2: {"type": b"{MKCoordinateSpan=dd}"}}},
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector("MKAddressFilter", b"initExcludingOptions:")
objc.registerNewKeywordsFromSelector("MKAddressFilter", b"initIncludingOptions:")
objc.registerNewKeywordsFromSelector(
    "MKAnnotationView", b"initWithAnnotation:reuseIdentifier:"
)
objc.registerNewKeywordsFromSelector("MKAnnotationView", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("MKCircleRenderer", b"initWithCircle:")
objc.registerNewKeywordsFromSelector(
    "MKClusterAnnotation", b"initWithMemberAnnotations:"
)
objc.registerNewKeywordsFromSelector("MKDirections", b"initWithRequest:")
objc.registerNewKeywordsFromSelector("MKDirectionsRequest", b"initWithContentsOfURL:")
objc.registerNewKeywordsFromSelector(
    "MKHybridMapConfiguration", b"initWithElevationStyle:"
)
objc.registerNewKeywordsFromSelector(
    "MKImageryMapConfiguration", b"initWithElevationStyle:"
)
objc.registerNewKeywordsFromSelector(
    "MKLocalPointsOfInterestRequest", b"initWithCenterCoordinate:radius:"
)
objc.registerNewKeywordsFromSelector(
    "MKLocalPointsOfInterestRequest", b"initWithCoordinateRegion:"
)
objc.registerNewKeywordsFromSelector(
    "MKLocalSearch", b"initWithPointsOfInterestRequest:"
)
objc.registerNewKeywordsFromSelector("MKLocalSearch", b"initWithRequest:")
objc.registerNewKeywordsFromSelector("MKLocalSearchRequest", b"initWithCompletion:")
objc.registerNewKeywordsFromSelector(
    "MKLocalSearchRequest", b"initWithNaturalLanguageQuery:"
)
objc.registerNewKeywordsFromSelector(
    "MKLocalSearchRequest", b"initWithNaturalLanguageQuery:region:"
)
objc.registerNewKeywordsFromSelector("MKLookAroundSceneRequest", b"initWithCoordinate:")
objc.registerNewKeywordsFromSelector("MKLookAroundSceneRequest", b"initWithMapItem:")
objc.registerNewKeywordsFromSelector(
    "MKLookAroundSnapshotter", b"initWithScene:options:"
)
objc.registerNewKeywordsFromSelector("MKLookAroundViewController", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "MKLookAroundViewController", b"initWithNibName:bundle:"
)
objc.registerNewKeywordsFromSelector("MKLookAroundViewController", b"initWithScene:")
objc.registerNewKeywordsFromSelector("MKMapCameraBoundary", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "MKMapCameraBoundary", b"initWithCoordinateRegion:"
)
objc.registerNewKeywordsFromSelector("MKMapCameraBoundary", b"initWithMapRect:")
objc.registerNewKeywordsFromSelector(
    "MKMapCameraZoomRange", b"initWithMaxCenterCoordinateDistance:"
)
objc.registerNewKeywordsFromSelector(
    "MKMapCameraZoomRange", b"initWithMinCenterCoordinateDistance:"
)
objc.registerNewKeywordsFromSelector(
    "MKMapCameraZoomRange",
    b"initWithMinCenterCoordinateDistance:maxCenterCoordinateDistance:",
)
objc.registerNewKeywordsFromSelector("MKMapItem", b"initWithPlacemark:")
objc.registerNewKeywordsFromSelector("MKMapItemAnnotation", b"initWithMapItem:")
objc.registerNewKeywordsFromSelector(
    "MKMapItemDetailViewController", b"initWithMapItem:"
)
objc.registerNewKeywordsFromSelector(
    "MKMapItemDetailViewController", b"initWithMapItem:displaysMap:"
)
objc.registerNewKeywordsFromSelector(
    "MKMapItemIdentifier", b"initWithIdentifierString:"
)
objc.registerNewKeywordsFromSelector(
    "MKMapItemRequest", b"initWithMapFeatureAnnotation:"
)
objc.registerNewKeywordsFromSelector("MKMapItemRequest", b"initWithMapItemIdentifier:")
objc.registerNewKeywordsFromSelector("MKMapSnapshotter", b"initWithOptions:")
objc.registerNewKeywordsFromSelector("MKMultiPolygon", b"initWithPolygons:")
objc.registerNewKeywordsFromSelector("MKMultiPolygonRenderer", b"initWithMultiPolygon:")
objc.registerNewKeywordsFromSelector("MKMultiPolyline", b"initWithPolylines:")
objc.registerNewKeywordsFromSelector(
    "MKMultiPolylineRenderer", b"initWithMultiPolyline:"
)
objc.registerNewKeywordsFromSelector("MKOverlayRenderer", b"initWithOverlay:")
objc.registerNewKeywordsFromSelector("MKPlacemark", b"initWithCoordinate:")
objc.registerNewKeywordsFromSelector(
    "MKPlacemark", b"initWithCoordinate:addressDictionary:"
)
objc.registerNewKeywordsFromSelector(
    "MKPlacemark", b"initWithCoordinate:postalAddress:"
)
objc.registerNewKeywordsFromSelector("MKPointAnnotation", b"initWithCoordinate:")
objc.registerNewKeywordsFromSelector(
    "MKPointAnnotation", b"initWithCoordinate:title:subtitle:"
)
objc.registerNewKeywordsFromSelector(
    "MKPointOfInterestFilter", b"initExcludingCategories:"
)
objc.registerNewKeywordsFromSelector(
    "MKPointOfInterestFilter", b"initIncludingCategories:"
)
objc.registerNewKeywordsFromSelector("MKPolygonRenderer", b"initWithPolygon:")
objc.registerNewKeywordsFromSelector("MKPolylineRenderer", b"initWithPolyline:")
objc.registerNewKeywordsFromSelector(
    "MKStandardMapConfiguration", b"initWithElevationStyle:"
)
objc.registerNewKeywordsFromSelector(
    "MKStandardMapConfiguration", b"initWithElevationStyle:emphasisStyle:"
)
objc.registerNewKeywordsFromSelector(
    "MKStandardMapConfiguration", b"initWithEmphasisStyle:"
)
objc.registerNewKeywordsFromSelector("MKTileOverlay", b"initWithURLTemplate:")
objc.registerNewKeywordsFromSelector("MKTileOverlayRenderer", b"initWithTileOverlay:")
expressions = {}

# END OF FILE
