ScreenCaptureKit/_ScreenCaptureKit.cpython-313-darwin.so,sha256=I1cPbvAy7grv0GWQNXj9uysTat2hAT7Q9_waX-8gLkg,69320
ScreenCaptureKit/__init__.py,sha256=qhA0ELVKu4mUynYX2TLrOKVTill-KYZuB-1DaCNMLy0,1926
ScreenCaptureKit/__pycache__/__init__.cpython-313.pyc,,
ScreenCaptureKit/__pycache__/_metadata.cpython-313.pyc,,
ScreenCaptureKit/_metadata.py,sha256=sQaBM1Smd76sK4Ty_flRUlAjburrSw2Bjnq0zcml44U,17808
pyobjc_framework_screencapturekit-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_screencapturekit-11.1.dist-info/METADATA,sha256=dpvDbKDW5rSca05ooJ2GjoaCzdIWI6yRoH8fM9sLzDU,2503
pyobjc_framework_screencapturekit-11.1.dist-info/RECORD,,
pyobjc_framework_screencapturekit-11.1.dist-info/WHEEL,sha256=memlX0NSEQnmSMa3rcNWPnk4cttudwgAZx3qq8qO4ME,115
pyobjc_framework_screencapturekit-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_screencapturekit-11.1.dist-info/top_level.txt,sha256=0p7LcTqLGS0eEZhKXDy19agwrnsrBZOa5dETQgy1190,17
