ApplicationServices/__init__.py,sha256=BzB1RSm72tZJALVoEOZ9JVfX-vZEJ4ANeZmCPaKND4Y,831
ApplicationServices/__pycache__/__init__.cpython-313.pyc,,
HIServices/_HIServices.cpython-313-darwin.so,sha256=WcaJT0Y_z42FUNf8ywpUZlL8RIZvwXkZd6F7aozgxCw,68000
HIServices/__init__.py,sha256=q9xL57g4Ee3depDNB5pvjj_pAV0grfx73S7sjQBKTug,878
HIServices/__pycache__/__init__.cpython-313.pyc,,
HIServices/__pycache__/_metadata.cpython-313.pyc,,
HIServices/_metadata.py,sha256=cqFVAlRa0zdm2VhAdg83tWS9HdwZ_QSi6VWo3c4UHYo,49213
PrintCore/_PrintCore.cpython-313-darwin.so,sha256=bSz3bXzdh3n0sJjPyTZXgYCJbXSflQzs_0UHxL2Xv9M,68320
PrintCore/__init__.py,sha256=8aZT54kXrR58kVZPl-zl_q1F9zFf-hT9TPQSCOZuRVE,1425
PrintCore/__pycache__/__init__.cpython-313.pyc,,
PrintCore/__pycache__/_metadata.cpython-313.pyc,,
PrintCore/_metadata.py,sha256=V9WTHq3qjvXqPoWPBZzMLTfT_pVQF3khaOccmrsbBjg,41148
pyobjc_framework_applicationservices-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_applicationservices-11.1.dist-info/METADATA,sha256=vxZM5Ocgi6DO9lwGQTrjimGd8_-43G1GVpWXWItWvyQ,2725
pyobjc_framework_applicationservices-11.1.dist-info/RECORD,,
pyobjc_framework_applicationservices-11.1.dist-info/WHEEL,sha256=memlX0NSEQnmSMa3rcNWPnk4cttudwgAZx3qq8qO4ME,115
pyobjc_framework_applicationservices-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_applicationservices-11.1.dist-info/top_level.txt,sha256=AIGBXHJ-VOAo8OSY17mbrfHVoOdFZhs6F_vmGENYrNI,41
