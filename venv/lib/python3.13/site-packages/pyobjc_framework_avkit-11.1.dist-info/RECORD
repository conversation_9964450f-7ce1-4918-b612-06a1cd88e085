AVKit/_AVKit.cpython-313-darwin.so,sha256=596KVM2tM78ehcLFMk99V5rcgg4cqhsvjAnv8NLvUIg,87008
AVKit/__init__.py,sha256=F3c0JgA4g7ky7V1OYD1gjvW7X_HJYZ3STgjjp296uKI,1138
AVKit/__pycache__/__init__.cpython-313.pyc,,
AVKit/__pycache__/_metadata.cpython-313.pyc,,
AVKit/_metadata.py,sha256=-aDiL6eg-PEQBzh2ynnkH4mM9u0VIEvYPv5ml-ITrek,14118
pyobjc_framework_avkit-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_avkit-11.1.dist-info/METADATA,sha256=icNbTTjL7gHSAbN7A3iWoy5ml9-KakefzBfr09f4AvI,2481
pyobjc_framework_avkit-11.1.dist-info/RECORD,,
pyobjc_framework_avkit-11.1.dist-info/WHEEL,sha256=memlX0NSEQnmSMa3rcNWPnk4cttudwgAZx3qq8qO4ME,115
pyobjc_framework_avkit-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_avkit-11.1.dist-info/top_level.txt,sha256=Ibd3kHY5SbEGLEyGZvzYljutRe3juSkcB4yN0VH4gHY,6
