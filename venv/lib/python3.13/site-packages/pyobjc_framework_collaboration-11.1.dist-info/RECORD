Collaboration/__init__.py,sha256=5Z2KlHZCQ22LjNOhXIGHprF3pM2JHw40ElRTY98V82s,860
Collaboration/__pycache__/__init__.cpython-313.pyc,,
Collaboration/__pycache__/_metadata.cpython-313.pyc,,
Collaboration/_metadata.py,sha256=0T3UoH9y97uAhVM7H_HOl8lLuIdPzmVOstGQJj1XIRA,2564
pyobjc_framework_collaboration-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_collaboration-11.1.dist-info/METADATA,sha256=bIr-Tax-c_FOW93MewhkUHR6Hn7p6kWRxHsGdMWcKZU,2626
pyobjc_framework_collaboration-11.1.dist-info/RECORD,,
pyobjc_framework_collaboration-11.1.dist-info/WHEEL,sha256=JNWh1Fm1UdwIQV075glCn4MVuCRs0sotJIq-J6rbxCU,109
pyobjc_framework_collaboration-11.1.dist-info/licenses/LICENSE.txt,sha256=DKBLB5KNSHK52bsiGHygQm3Yv6sI8m6toJmacdyBqv8,1249
pyobjc_framework_collaboration-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_collaboration-11.1.dist-info/top_level.txt,sha256=3l1JMAAyZ9thzrlKjaFYOL9Ut4mIfao-uxDyMme7GwI,14
