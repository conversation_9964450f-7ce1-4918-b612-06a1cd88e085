ClassKit/_ClassKit.cpython-313-darwin.so,sha256=4MhXIN4u1Aj9KzECX3bkvIGe1VxUxMWbTk8Lxrdhgu4,67904
ClassKit/__init__.py,sha256=Cbo5u141qVVb1S08DzihhzXlxakh39_SBRodbRHEloc,1242
ClassKit/__pycache__/__init__.cpython-313.pyc,,
ClassKit/__pycache__/_metadata.cpython-313.pyc,,
ClassKit/_metadata.py,sha256=bUWZuO6vsnjPMe55EZPjYyZXyBbLLEu7pn5RsS62ecs,8736
pyobjc_framework_classkit-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_classkit-11.1.dist-info/METADATA,sha256=Qesj9n8XiByqaz1zlGUEtl7Dn_QHHX2ffghF5qaRB4Y,2423
pyobjc_framework_classkit-11.1.dist-info/RECORD,,
pyobjc_framework_classkit-11.1.dist-info/WHEEL,sha256=memlX0NSEQnmSMa3rcNWPnk4cttudwgAZx3qq8qO4ME,115
pyobjc_framework_classkit-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_classkit-11.1.dist-info/top_level.txt,sha256=zWlN-sn0toCagY8VF-aAWbAGlGpNlF-6SjM_YUrVemI,9
