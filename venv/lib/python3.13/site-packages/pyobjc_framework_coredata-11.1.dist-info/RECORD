CoreData/_CoreData.cpython-313-darwin.so,sha256=xqiPGLcCwItUaYZ378Sq8ggAN0n-4qFh4k3lkyVzqUE,68000
CoreData/__init__.py,sha256=9yg_HjJ8Z0Jz6BQ5zWXgFg03KR_zXxYuiDJhKUBEvlo,1672
CoreData/__pycache__/__init__.cpython-313.pyc,,
CoreData/__pycache__/_convenience.cpython-313.pyc,,
CoreData/__pycache__/_metadata.cpython-313.pyc,,
CoreData/_convenience.py,sha256=K4AMOX8llx0APHBQgk9Mxc7sUK1-l6Sz2B3xkcK9p2o,1764
CoreData/_metadata.py,sha256=0rOyKGvT8YD_eZ1PWqr2WlWhpwVXzM2ARr_9Zegjyh8,51256
pyobjc_framework_coredata-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_coredata-11.1.dist-info/METADATA,sha256=MtDlHG3gq4KwWFanNDHRyUSD7KI3f3TUoGwhQ1J-U8M,2633
pyobjc_framework_coredata-11.1.dist-info/RECORD,,
pyobjc_framework_coredata-11.1.dist-info/WHEEL,sha256=memlX0NSEQnmSMa3rcNWPnk4cttudwgAZx3qq8qO4ME,115
pyobjc_framework_coredata-11.1.dist-info/licenses/LICENSE.txt,sha256=DKBLB5KNSHK52bsiGHygQm3Yv6sI8m6toJmacdyBqv8,1249
pyobjc_framework_coredata-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_coredata-11.1.dist-info/top_level.txt,sha256=6cRCSiJ5-kDbx3eEep6NvyBVrvLxuOB3XRQKYwkdSfo,9
