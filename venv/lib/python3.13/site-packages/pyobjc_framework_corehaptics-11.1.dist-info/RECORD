CoreHaptics/__init__.py,sha256=_9yQ8u88XPyQJi8pWE6EKrhY2aQcLupkfDiLI9ZMtq8,1234
CoreHaptics/__pycache__/__init__.cpython-313.pyc,,
CoreHaptics/__pycache__/_metadata.cpython-313.pyc,,
CoreHaptics/_metadata.py,sha256=nAAAZmLC7lCJWrnMeT__HtOiz2N6jpeir1WxKZdxYPk,15752
pyobjc_framework_corehaptics-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_corehaptics-11.1.dist-info/METADATA,sha256=PiauSyjnZsopBnb6peUUamUDDKmWq5Nqg92tIev-n_g,2436
pyobjc_framework_corehaptics-11.1.dist-info/RECORD,,
pyobjc_framework_corehaptics-11.1.dist-info/WHEEL,sha256=JNWh1Fm1UdwIQV075glCn4MVuCRs0sotJIq-J6rbxCU,109
pyobjc_framework_corehaptics-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_corehaptics-11.1.dist-info/top_level.txt,sha256=Eo7NHy2ObKy4wiG-326QZq5UG2NJzeNk5Ty_rnYUAsY,12
