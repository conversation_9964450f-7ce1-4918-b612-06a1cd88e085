"""
Python mapping for the InstantMessage framework.

This module does not contain docstrings for the wrapped code, check Apple's
documentation for details on how to use these functions and classes.
"""


def _setup():
    import sys

    import Foundation
    import Quartz
    import objc
    from . import _metadata

    dir_func, getattr_func = objc.createFrameworkDirAndGetattr(
        name="InstantMessage",
        frameworkIdentifier="com.apple.iChat.InstantMessage",
        frameworkPath=objc.pathForFramework(
            "/System/Library/Frameworks/InstantMessage.framework"
        ),
        globals_dict=globals(),
        inline_list=None,
        parents=(Foundation, Quartz),
        metadict=_metadata.__dict__,
    )

    globals()["__dir__"] = dir_func
    globals()["__getattr__"] = getattr_func

    del sys.modules["InstantMessage._metadata"]


globals().pop("_setup")()
