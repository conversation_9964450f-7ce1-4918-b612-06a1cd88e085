CFNetwork/__init__.py,sha256=7a7L-aVgktmWoJMwdna1YFtMIKZJT4hkg40pdPGHb9Q,1167
CFNetwork/__pycache__/__init__.cpython-313.pyc,,
CFNetwork/__pycache__/_metadata.cpython-313.pyc,,
CFNetwork/_manual.cpython-313-darwin.so,sha256=Y9nXoyAX8daO4lFLuz8lOVEPuFNKOa5BxIJkaNv87MQ,87296
CFNetwork/_metadata.py,sha256=aPGCVmWmTOwsNrlcZOv-rPRgrbr68e53udCZqaVMj7k,26348
pyobjc_framework_cfnetwork-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_cfnetwork-11.1.dist-info/METADATA,sha256=TKMOctTrUKwfvGFFf38JLpxqMy0l9-J53Yh9vVPbe3M,2729
pyobjc_framework_cfnetwork-11.1.dist-info/RECORD,,
pyobjc_framework_cfnetwork-11.1.dist-info/WHEEL,sha256=memlX0NSEQnmSMa3rcNWPnk4cttudwgAZx3qq8qO4ME,115
pyobjc_framework_cfnetwork-11.1.dist-info/licenses/LICENSE.txt,sha256=DKBLB5KNSHK52bsiGHygQm3Yv6sI8m6toJmacdyBqv8,1249
pyobjc_framework_cfnetwork-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_cfnetwork-11.1.dist-info/top_level.txt,sha256=q2Z9JhHHv2Q8zzn9XMN_mb4IrMeGTqi4Ic62XPVuKGA,10
