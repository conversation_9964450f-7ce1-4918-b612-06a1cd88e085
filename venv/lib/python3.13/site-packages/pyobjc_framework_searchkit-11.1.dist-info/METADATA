Metadata-Version: 2.4
Name: pyobjc-framework-SearchKit
Version: 11.1
Summary: Wrappers for the framework SearchK<PERSON> on macOS
Home-page: https://github.com/ronal<PERSON><PERSON>oren/pyobjc
Author: <PERSON>
Author-email: <EMAIL>
License: MIT
Keywords: PyObjC,SearchKit
Platform: MacOS X (>=10.5)
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: MacOS X :: Cocoa
Classifier: Intended Audience :: Developers
Classifier: Natural Language :: English
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3.14
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Objective C
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Software Development :: User Interfaces
Requires-Python: >=3.9
Description-Content-Type: text/x-rst; charset=UTF-8
License-File: LICENSE.txt
Requires-Dist: pyobjc-core>=11.1
Requires-Dist: pyobjc-framework-CoreServices>=11.1
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: platform
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary
Project-URL: Documentation, https://pyobjc.readthedocs.io/en/latest/
Project-URL: Issue tracker, https://github.com/ronaldoussoren/pyobjc/issues
Project-URL: Repository, https://github.com/ronaldoussoren/pyobjc


Deprecated wrappers for the "SearchKit" framework on macOS.

Use the CoreServices package instead.


Project links
-------------

* `Documentation <https://pyobjc.readthedocs.io/en/latest/>`_

* `Issue Tracker <https://github.com/ronaldoussoren/pyobjc/issues>`_

* `Repository <https://github.com/ronaldoussoren/pyobjc/>`_

