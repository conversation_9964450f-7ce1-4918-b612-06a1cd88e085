QuickLookThumbnailing/__init__.py,sha256=an_UIhuNm9t2qVlXWB-akXgVk8Xj7ELxFFdiqNEl-hk,1119
QuickLookThumbnailing/__pycache__/__init__.cpython-313.pyc,,
QuickLookThumbnailing/__pycache__/_metadata.cpython-313.pyc,,
QuickLookThumbnailing/_metadata.py,sha256=fN80IVZuRuX4GpySxNvX6Lm3wqrEQoAduY9eABfGK5U,5180
pyobjc_framework_quicklookthumbnailing-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_quicklookthumbnailing-11.1.dist-info/METADATA,sha256=USC2G1rEZvQNnAYst2ZY914o80tWkAWNcSVUk3-0Sfk,2521
pyobjc_framework_quicklookthumbnailing-11.1.dist-info/RECORD,,
pyobjc_framework_quicklookthumbnailing-11.1.dist-info/WHEEL,sha256=JNWh1Fm1UdwIQV075glCn4MVuCRs0sotJIq-J6rbxCU,109
pyobjc_framework_quicklookthumbnailing-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_quicklookthumbnailing-11.1.dist-info/top_level.txt,sha256=ySt4lENzXNTxFeDtbGa17u9Zj_qdOt20QV0ywFn7zCU,22
