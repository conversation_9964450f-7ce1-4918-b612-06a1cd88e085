MetalKit/_MetalKit.cpython-313-darwin.so,sha256=8MRMy6JKPaPJIl-rz1SWXNQqXSzVu0Yh4mMqnJml06w,67888
MetalKit/__init__.py,sha256=HXNTPMQmlpAyNT3c5jbTItcLFxHwb8QPm1QZB24swE8,1189
MetalKit/__pycache__/__init__.cpython-313.pyc,,
MetalKit/__pycache__/_metadata.cpython-313.pyc,,
MetalKit/_metadata.py,sha256=PxGGQAiN454rlPW0kI9zpNpHWLCAHIFQOIt0Zr6ySp4,9867
pyobjc_framework_metalkit-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_metalkit-11.1.dist-info/METADATA,sha256=r3dhx30hisYyflobbcOlkbndUjr1pZJOs4ArkADUS2s,2468
pyobjc_framework_metalkit-11.1.dist-info/RECORD,,
pyobjc_framework_metalkit-11.1.dist-info/WHEEL,sha256=memlX0NSEQnmSMa3rcNWPnk4cttudwgAZx3qq8qO4ME,115
pyobjc_framework_metalkit-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_metalkit-11.1.dist-info/top_level.txt,sha256=wVn-JMvt4_AKAc6WpPMQ8Kllolacq2mpPf-a87DqoCQ,9
