# This file is generated by objective.metadata
#
# Last update: Tue Jun 11 10:19:10 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
misc.update(
    {
        "SCNVector4": objc.createStructType(
            "SceneKit.SCNVector4", b"{SCNVector4=dddd}", ["x", "y", "z", "w"]
        ),
        "SCNVector3": objc.createStructType(
            "SceneKit.SCNVector3", b"{SCNVector3=ddd}", ["x", "y", "z"]
        ),
    }
)
constants = """$SCNConsistencyElementIDErrorKey$SCNConsistencyElementTypeErrorKey$SCNConsistencyLineNumberErrorKey$SCNDetailedErrorsKey$SCNErrorDomain$SCNGeometrySourceSemanticBoneIndices$SCNGeometrySourceSemanticBoneWeights$SCNGeometrySourceSemanticColor$SCNGeometrySourceSemanticEdgeCrease$SCNGeometrySourceSemanticNormal$SCNGeometrySourceSemanticTangent$SCNGeometrySourceSemanticTexcoord$SCNGeometrySourceSemanticVertex$SCNGeometrySourceSemanticVertexCrease$SCNHitTestBackFaceCullingKey$SCNHitTestBoundingBoxOnlyKey$SCNHitTestClipToZRangeKey$SCNHitTestFirstFoundOnlyKey$SCNHitTestIgnoreChildNodesKey$SCNHitTestIgnoreHiddenNodesKey$SCNHitTestOptionCategoryBitMask$SCNHitTestOptionIgnoreLightArea$SCNHitTestOptionSearchMode$SCNHitTestRootNodeKey$SCNHitTestSortResultsKey$SCNLightAttenuationEndKey$SCNLightAttenuationFalloffExponentKey$SCNLightAttenuationStartKey$SCNLightShadowFarClippingKey$SCNLightShadowNearClippingKey$SCNLightSpotInnerAngleKey$SCNLightSpotOuterAngleKey$SCNLightTypeAmbient$SCNLightTypeArea$SCNLightTypeDirectional$SCNLightTypeIES$SCNLightTypeOmni$SCNLightTypeProbe$SCNLightTypeSpot$SCNLightingModelBlinn$SCNLightingModelConstant$SCNLightingModelLambert$SCNLightingModelPhong$SCNLightingModelPhysicallyBased$SCNLightingModelShadowOnly$SCNMatrix4Identity@{CATransform3D=dddddddddddddddd}$SCNModelTransform$SCNModelViewProjectionTransform$SCNModelViewTransform$SCNNormalTransform$SCNParticlePropertyAngle$SCNParticlePropertyAngularVelocity$SCNParticlePropertyBounce$SCNParticlePropertyCharge$SCNParticlePropertyColor$SCNParticlePropertyContactNormal$SCNParticlePropertyContactPoint$SCNParticlePropertyFrame$SCNParticlePropertyFrameRate$SCNParticlePropertyFriction$SCNParticlePropertyLife$SCNParticlePropertyOpacity$SCNParticlePropertyPosition$SCNParticlePropertyRotationAxis$SCNParticlePropertySize$SCNParticlePropertyVelocity$SCNPhysicsShapeKeepAsCompoundKey$SCNPhysicsShapeOptionCollisionMargin$SCNPhysicsShapeScaleKey$SCNPhysicsShapeTypeBoundingBox$SCNPhysicsShapeTypeConcavePolyhedron$SCNPhysicsShapeTypeConvexHull$SCNPhysicsShapeTypeKey$SCNPhysicsTestBackfaceCullingKey$SCNPhysicsTestCollisionBitMaskKey$SCNPhysicsTestSearchModeAll$SCNPhysicsTestSearchModeAny$SCNPhysicsTestSearchModeClosest$SCNPhysicsTestSearchModeKey$SCNPreferLowPowerDeviceKey$SCNPreferredDeviceKey$SCNPreferredRenderingAPIKey$SCNProgramMappingChannelKey$SCNProjectionTransform$SCNSceneEndTimeAttributeKey$SCNSceneExportDestinationURL$SCNSceneFrameRateAttributeKey$SCNSceneSourceAnimationImportPolicyDoNotPlay$SCNSceneSourceAnimationImportPolicyKey$SCNSceneSourceAnimationImportPolicyPlay$SCNSceneSourceAnimationImportPolicyPlayRepeatedly$SCNSceneSourceAnimationImportPolicyPlayUsingSceneTimeBase$SCNSceneSourceAssetAuthorKey$SCNSceneSourceAssetAuthoringToolKey$SCNSceneSourceAssetContributorsKey$SCNSceneSourceAssetCreatedDateKey$SCNSceneSourceAssetDirectoryURLsKey$SCNSceneSourceAssetModifiedDateKey$SCNSceneSourceAssetUnitKey$SCNSceneSourceAssetUnitMeterKey$SCNSceneSourceAssetUnitNameKey$SCNSceneSourceAssetUpAxisKey$SCNSceneSourceCheckConsistencyKey$SCNSceneSourceConvertToYUpKey$SCNSceneSourceConvertUnitsToMetersKey$SCNSceneSourceCreateNormalsIfAbsentKey$SCNSceneSourceFlattenSceneKey$SCNSceneSourceLoadingOptionPreserveOriginalTopology$SCNSceneSourceOverrideAssetURLsKey$SCNSceneSourceStrictConformanceKey$SCNSceneSourceUseSafeModeKey$SCNSceneStartTimeAttributeKey$SCNSceneUpAxisAttributeKey$SCNShaderModifierEntryPointFragment$SCNShaderModifierEntryPointGeometry$SCNShaderModifierEntryPointLightingModel$SCNShaderModifierEntryPointSurface$SCNVector3Zero@{SCNVector3=ddd}$SCNVector4Zero@{SCNVector4=dddd}$SCNViewTransform$"""
enums = """$SCNActionTimingModeEaseIn@1$SCNActionTimingModeEaseInEaseOut@3$SCNActionTimingModeEaseOut@2$SCNActionTimingModeLinear@0$SCNAntialiasingModeMultisampling16X@4$SCNAntialiasingModeMultisampling2X@1$SCNAntialiasingModeMultisampling4X@2$SCNAntialiasingModeMultisampling8X@3$SCNAntialiasingModeNone@0$SCNBillboardAxisAll@7$SCNBillboardAxisX@1$SCNBillboardAxisY@2$SCNBillboardAxisZ@4$SCNBlendModeAdd@1$SCNBlendModeAlpha@0$SCNBlendModeMax@6$SCNBlendModeMultiply@3$SCNBlendModeReplace@5$SCNBlendModeScreen@4$SCNBlendModeSubtract@2$SCNBufferFrequencyPerFrame@0$SCNBufferFrequencyPerNode@1$SCNBufferFrequencyPerShadable@2$SCNCameraProjectionDirectionHorizontal@1$SCNCameraProjectionDirectionVertical@0$SCNChamferModeBack@2$SCNChamferModeBoth@0$SCNChamferModeFront@1$SCNColorMaskAll@15$SCNColorMaskAlpha@1$SCNColorMaskBlue@2$SCNColorMaskGreen@4$SCNColorMaskNone@0$SCNColorMaskRed@8$SCNConsistencyInvalidArgumentError@1002$SCNConsistencyInvalidCountError@1001$SCNConsistencyInvalidURIError@1000$SCNConsistencyMissingAttributeError@1004$SCNConsistencyMissingElementError@1003$SCNConsistencyXMLSchemaValidationError@1005$SCNCullModeBack@0$SCNCullModeFront@1$SCNDebugOptionNone@0$SCNDebugOptionRenderAsWireframe@64$SCNDebugOptionShowBoundingBoxes@2$SCNDebugOptionShowCameras@1024$SCNDebugOptionShowConstraints@512$SCNDebugOptionShowCreases@256$SCNDebugOptionShowLightExtents@8$SCNDebugOptionShowLightInfluences@4$SCNDebugOptionShowPhysicsFields@16$SCNDebugOptionShowPhysicsShapes@1$SCNDebugOptionShowSkeletons@128$SCNDebugOptionShowWireframe@32$SCNFillModeFill@0$SCNFillModeLines@1$SCNFilterModeLinear@2$SCNFilterModeNearest@1$SCNFilterModeNone@0$SCNGeometryPrimitiveTypeLine@2$SCNGeometryPrimitiveTypePoint@3$SCNGeometryPrimitiveTypePolygon@4$SCNGeometryPrimitiveTypeTriangleStrip@1$SCNGeometryPrimitiveTypeTriangles@0$SCNHitTestSearchModeAll@1$SCNHitTestSearchModeAny@2$SCNHitTestSearchModeClosest@0$SCNInteractionModeFly@0$SCNInteractionModeOrbitAngleMapping@2$SCNInteractionModeOrbitArcball@4$SCNInteractionModeOrbitCenteredArcball@3$SCNInteractionModeOrbitTurntable@1$SCNInteractionModePan@5$SCNInteractionModeTruck@6$SCNLightAreaTypePolygon@4$SCNLightAreaTypeRectangle@1$SCNLightProbeTypeIrradiance@0$SCNLightProbeTypeRadiance@1$SCNLightProbeUpdateTypeNever@0$SCNLightProbeUpdateTypeRealtime@1$SCNMorpherCalculationModeAdditive@1$SCNMorpherCalculationModeNormalized@0$SCNMovabilityHintFixed@0$SCNMovabilityHintMovable@1$SCNNodeFocusBehaviorFocusable@2$SCNNodeFocusBehaviorNone@0$SCNNodeFocusBehaviorOccluding@1$SCNParticleBirthDirectionConstant@0$SCNParticleBirthDirectionRandom@2$SCNParticleBirthDirectionSurfaceNormal@1$SCNParticleBirthLocationSurface@0$SCNParticleBirthLocationVertex@2$SCNParticleBirthLocationVolume@1$SCNParticleBlendModeAdditive@0$SCNParticleBlendModeAlpha@4$SCNParticleBlendModeMultiply@2$SCNParticleBlendModeReplace@5$SCNParticleBlendModeScreen@3$SCNParticleBlendModeSubtract@1$SCNParticleEventBirth@0$SCNParticleEventCollision@2$SCNParticleEventDeath@1$SCNParticleImageSequenceAnimationModeAutoReverse@2$SCNParticleImageSequenceAnimationModeClamp@1$SCNParticleImageSequenceAnimationModeRepeat@0$SCNParticleInputModeOverDistance@1$SCNParticleInputModeOverLife@0$SCNParticleInputModeOverOtherProperty@2$SCNParticleModifierStagePostCollision@3$SCNParticleModifierStagePostDynamics@1$SCNParticleModifierStagePreCollision@2$SCNParticleModifierStagePreDynamics@0$SCNParticleOrientationModeBillboardScreenAligned@0$SCNParticleOrientationModeBillboardViewAligned@1$SCNParticleOrientationModeBillboardYAligned@3$SCNParticleOrientationModeFree@2$SCNParticleSortingModeDistance@2$SCNParticleSortingModeNone@0$SCNParticleSortingModeOldestFirst@3$SCNParticleSortingModeProjectedDepth@1$SCNParticleSortingModeYoungestFirst@4$SCNPhysicsBodyTypeDynamic@1$SCNPhysicsBodyTypeKinematic@2$SCNPhysicsBodyTypeStatic@0$SCNPhysicsCollisionCategoryAll@18446744073709551615$SCNPhysicsCollisionCategoryDefault@1$SCNPhysicsCollisionCategoryStatic@2$SCNPhysicsFieldScopeInsideExtent@0$SCNPhysicsFieldScopeOutsideExtent@1$SCNProgramCompilationError@1$SCNReferenceLoadingPolicyImmediate@0$SCNReferenceLoadingPolicyOnDemand@1$SCNRenderingAPIMetal@0$SCNRenderingAPIOpenGLCore32@2$SCNRenderingAPIOpenGLCore41@3$SCNRenderingAPIOpenGLLegacy@1$SCNSceneSourceStatusComplete@16$SCNSceneSourceStatusError@-1$SCNSceneSourceStatusParsing@4$SCNSceneSourceStatusProcessing@12$SCNSceneSourceStatusValidating@8$SCNShadowModeDeferred@1$SCNShadowModeForward@0$SCNShadowModeModulated@2$SCNTessellationSmoothingModeNone@0$SCNTessellationSmoothingModePNTriangles@1$SCNTessellationSmoothingModePhong@2$SCNTransparencyModeAOne@0$SCNTransparencyModeDefault@0$SCNTransparencyModeDualLayer@3$SCNTransparencyModeRGBZero@1$SCNTransparencyModeSingleLayer@2$SCNWrapModeClamp@1$SCNWrapModeClampToBorder@3$SCNWrapModeMirror@4$SCNWrapModeRepeat@2$SCN_ENABLE_METAL@1$SCN_ENABLE_OPENGL@1$"""
misc.update(
    {
        "SCNLightProbeType": NewType("SCNLightProbeType", int),
        "SCNBillboardAxis": NewType("SCNBillboardAxis", int),
        "SCNCullMode": NewType("SCNCullMode", int),
        "SCNFillMode": NewType("SCNFillMode", int),
        "SCNMovabilityHint": NewType("SCNMovabilityHint", int),
        "SCNTransparencyMode": NewType("SCNTransparencyMode", int),
        "SCNPhysicsCollisionCategory": NewType("SCNPhysicsCollisionCategory", int),
        "SCNSceneSourceStatus": NewType("SCNSceneSourceStatus", int),
        "SCNChamferMode": NewType("SCNChamferMode", int),
        "SCNMorpherCalculationMode": NewType("SCNMorpherCalculationMode", int),
        "SCNBufferFrequency": NewType("SCNBufferFrequency", int),
        "SCNParticleImageSequenceAnimationMode": NewType(
            "SCNParticleImageSequenceAnimationMode", int
        ),
        "SCNParticleModifierStage": NewType("SCNParticleModifierStage", int),
        "SCNInteractionMode": NewType("SCNInteractionMode", int),
        "SCNFilterMode": NewType("SCNFilterMode", int),
        "SCNShadowMode": NewType("SCNShadowMode", int),
        "SCNParticleEvent": NewType("SCNParticleEvent", int),
        "SCNActionTimingMode": NewType("SCNActionTimingMode", int),
        "SCNGeometryPrimitiveType": NewType("SCNGeometryPrimitiveType", int),
        "SCNParticleBlendMode": NewType("SCNParticleBlendMode", int),
        "SCNWrapMode": NewType("SCNWrapMode", int),
        "SCNHitTestSearchMode": NewType("SCNHitTestSearchMode", int),
        "SCNLightProbeUpdateType": NewType("SCNLightProbeUpdateType", int),
        "SCNReferenceLoadingPolicy": NewType("SCNReferenceLoadingPolicy", int),
        "SCNParticleOrientationMode": NewType("SCNParticleOrientationMode", int),
        "SCNPhysicsBodyType": NewType("SCNPhysicsBodyType", int),
        "SCNNodeFocusBehavior": NewType("SCNNodeFocusBehavior", int),
        "SCNParticleBirthDirection": NewType("SCNParticleBirthDirection", int),
        "SCNTessellationSmoothingMode": NewType("SCNTessellationSmoothingMode", int),
        "SCNRenderingAPI": NewType("SCNRenderingAPI", int),
        "SCNParticleSortingMode": NewType("SCNParticleSortingMode", int),
        "SCNColorMask": NewType("SCNColorMask", int),
        "SCNDebugOptions": NewType("SCNDebugOptions", int),
        "SCNPhysicsFieldScope": NewType("SCNPhysicsFieldScope", int),
        "SCNBlendMode": NewType("SCNBlendMode", int),
        "SCNParticleInputMode": NewType("SCNParticleInputMode", int),
        "SCNAntialiasingMode": NewType("SCNAntialiasingMode", int),
        "SCNParticleBirthLocation": NewType("SCNParticleBirthLocation", int),
        "SCNCameraProjectionDirection": NewType("SCNCameraProjectionDirection", int),
        "SCNLightAreaType": NewType("SCNLightAreaType", int),
    }
)
misc.update(
    {
        "SCNPhysicsTestOption": NewType("SCNPhysicsTestOption", str),
        "SCNLightType": NewType("SCNLightType", str),
        "SCNSceneSourceAnimationImportPolicy": NewType(
            "SCNSceneSourceAnimationImportPolicy", str
        ),
        "SCNLightingModel": NewType("SCNLightingModel", str),
        "SCNPhysicsTestSearchMode": NewType("SCNPhysicsTestSearchMode", str),
        "SCNShaderModifierEntryPoint": NewType("SCNShaderModifierEntryPoint", str),
        "SCNSceneSourceLoadingOption": NewType("SCNSceneSourceLoadingOption", str),
        "SCNParticleProperty": NewType("SCNParticleProperty", str),
        "SCNPhysicsShapeOption": NewType("SCNPhysicsShapeOption", str),
        "SCNSceneAttribute": NewType("SCNSceneAttribute", str),
        "SCNHitTestOption": NewType("SCNHitTestOption", str),
        "SCNViewOption": NewType("SCNViewOption", str),
        "SCNPhysicsShapeType": NewType("SCNPhysicsShapeType", str),
    }
)
misc.update({})
functions = {
    "SCNMatrix4IsIdentity": (b"B{CATransform3D=dddddddddddddddd}",),
    "SCNVector4Make": (b"{SCNVector4=dddd}dddd",),
    "SCNMatrix4EqualToMatrix4": (
        b"B{CATransform3D=dddddddddddddddd}{CATransform3D=dddddddddddddddd}",
    ),
    "SCNMatrix4Mult": (
        b"{CATransform3D=dddddddddddddddd}{CATransform3D=dddddddddddddddd}{CATransform3D=dddddddddddddddd}",
    ),
    "SCNMatrix4Scale": (
        b"{CATransform3D=dddddddddddddddd}{CATransform3D=dddddddddddddddd}ddd",
    ),
    "SCNMatrix4MakeRotation": (b"{CATransform3D=dddddddddddddddd}dddd",),
    "SCNExportJavaScriptModule": (b"v@",),
    "SCNVector3EqualToVector3": (b"B{SCNVector3=ddd}{SCNVector3=ddd}",),
    "SCNMatrix4ToGLKMatrix4": (b"[16f]{CATransform3D=dddddddddddddddd}",),
    "SCNVector4FromGLKVector4": (b"{SCNVector4=dddd}[4f]",),
    "SCNVector3ToGLKVector3": (b"[3f]{SCNVector3=ddd}",),
    "SCNMatrix4MakeScale": (b"{CATransform3D=dddddddddddddddd}ddd",),
    "SCNMatrix4FromGLKMatrix4": (b"{CATransform3D=dddddddddddddddd}[16f]",),
    "SCNVector3ToFloat3": (b"<3f>{SCNVector3=ddd}",),
    "SCNMatrix4Invert": (
        b"{CATransform3D=dddddddddddddddd}{CATransform3D=dddddddddddddddd}",
    ),
    "SCNVector4ToGLKVector4": (b"[4f]{SCNVector4=dddd}",),
    "SCNMatrix4Rotate": (
        b"{CATransform3D=dddddddddddddddd}{CATransform3D=dddddddddddddddd}dddd",
    ),
    "SCNVector4EqualToVector4": (b"B{SCNVector4=dddd}{SCNVector4=dddd}",),
    "SCNVector3Make": (b"{SCNVector3=ddd}ddd",),
    "SCNVector3FromGLKVector3": (b"{SCNVector3=ddd}[3f]",),
    "SCNVector3FromFloat3": (b"{SCNVector3=ddd}<3f>",),
    "SCNMatrix4MakeTranslation": (b"{CATransform3D=dddddddddddddddd}ddd",),
    "SCNMatrix4Translate": (
        b"{CATransform3D=dddddddddddddddd}{CATransform3D=dddddddddddddddd}ddd",
    ),
}
aliases = {
    "SCNHitTestOptionSortResults": "SCNHitTestSortResultsKey",
    "SCNPhysicsTestOptionSearchMode": "SCNPhysicsTestSearchModeKey",
    "SCNHitTestOptionIgnoreHiddenNodes": "SCNHitTestIgnoreHiddenNodesKey",
    "SCNMatrix4": "CATransform3D",
    "SCNSceneSourceLoadingOptionStrictConformance": "SCNSceneSourceStrictConformanceKey",
    "SCNHitTestOptionClipToZRange": "SCNHitTestClipToZRangeKey",
    "SCNQuaternion": "SCNVector4",
    "SCNSceneSourceLoadingOptionOverrideAssetURLs": "SCNSceneSourceOverrideAssetURLsKey",
    "SCNViewOptionPreferredRenderingAPI": "SCNPreferredRenderingAPIKey",
    "SCNSceneAttributeEndTime": "SCNSceneEndTimeAttributeKey",
    "SCNViewOptionPreferredDevice": "SCNPreferredDeviceKey",
    "SCNHitTestOptionFirstFoundOnly": "SCNHitTestFirstFoundOnlyKey",
    "SCNSceneSourceLoadingOptionAnimationImportPolicy": "SCNSceneSourceAnimationImportPolicyKey",
    "SCNSceneSourceLoadingOptionCheckConsistency": "SCNSceneSourceCheckConsistencyKey",
    "SCNSceneSourceLoadingOptionFlattenScene": "SCNSceneSourceFlattenSceneKey",
    "SCNPhysicsShapeOptionKeepAsCompound": "SCNPhysicsShapeKeepAsCompoundKey",
    "SCNSceneSourceLoadingOptionAssetDirectoryURLs": "SCNSceneSourceAssetDirectoryURLsKey",
    "SCNPhysicsTestOptionBackfaceCulling": "SCNPhysicsTestBackfaceCullingKey",
    "SCNPhysicsShapeOptionScale": "SCNPhysicsShapeScaleKey",
    "SCNViewOptionPreferLowPowerDevice": "SCNPreferLowPowerDeviceKey",
    "SCNTransparencyModeDefault": "SCNTransparencyModeAOne",
    "SCNSceneSourceLoadingOptionConvertUnitsToMeters": "SCNSceneSourceConvertUnitsToMetersKey",
    "SCNColor": "NSColor",
    "SCNSceneSourceLoadingOptionUseSafeMode": "SCNSceneSourceUseSafeModeKey",
    "SCNSceneAttributeStartTime": "SCNSceneStartTimeAttributeKey",
    "SCNPhysicsTestOptionCollisionBitMask": "SCNPhysicsTestCollisionBitMaskKey",
    "SCNSceneAttributeFrameRate": "SCNSceneFrameRateAttributeKey",
    "SCNHitTestOptionRootNode": "SCNHitTestRootNodeKey",
    "SCNHitTestOptionBackFaceCulling": "SCNHitTestBackFaceCullingKey",
    "SCNHitTestOptionBoundingBoxOnly": "SCNHitTestBoundingBoxOnlyKey",
    "SCNSceneAttributeUpAxis": "SCNSceneUpAxisAttributeKey",
    "SCNSceneSourceLoadingOptionCreateNormalsIfAbsent": "SCNSceneSourceCreateNormalsIfAbsentKey",
    "SCNHitTestOptionIgnoreChildNodes": "SCNHitTestIgnoreChildNodesKey",
    "SCNSceneSourceLoadingOptionConvertToYUp": "SCNSceneSourceConvertToYUpKey",
    "SCNCullBack": "SCNCullModeBack",
    "SCNPhysicsShapeOptionType": "SCNPhysicsShapeTypeKey",
    "SCNCullFront": "SCNCullModeFront",
}
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(b"CAAnimation", b"setUsesSceneTimeBase:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CAAnimation", b"usesSceneTimeBase", {"retval": {"type": b"Z"}})
    r(
        b"NSObject",
        b"actionForKey:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"actionKeys", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"addAnimation:forKey:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"addAnimationPlayer:forKey:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(b"NSObject", b"allowsTranslation", {"required": True, "retval": {"type": "Z"}})
    r(
        b"NSObject",
        b"animationForKey:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"animationKeys", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"animationPlayerForKey:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"audioEngine", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"audioEnvironmentNode",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(b"NSObject", b"audioListener", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"autoSwitchToFreeCamera",
        {"required": True, "retval": {"type": "Z"}},
    )
    r(
        b"NSObject",
        b"autoenablesDefaultLighting",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"avoidOccluderConstraint:didAvoidOccluder:forNode:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"avoidOccluderConstraint:shouldAvoidOccluder:forNode:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"cameraInertiaDidEndForController:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"cameraInertiaWillStartForController:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"colorPixelFormat", {"required": True, "retval": {"type": "Q"}})
    r(b"NSObject", b"commandQueue", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"context", {"required": True, "retval": {"type": b"^v"}})
    r(
        b"NSObject",
        b"currentRenderCommandEncoder",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"currentRenderPassDescriptor",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(b"NSObject", b"currentTime", {"required": True, "retval": {"type": b"d"}})
    r(
        b"NSObject",
        b"currentViewport",
        {"required": True, "retval": {"type": b"{CGRect={CGPoint=dd}{CGSize=dd}}"}},
    )
    r(b"NSObject", b"debugOptions", {"required": True, "retval": {"type": "Q"}})
    r(b"NSObject", b"delegate", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"depthPixelFormat", {"required": True, "retval": {"type": "Q"}})
    r(b"NSObject", b"device", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"flyModeVelocity", {"required": True, "retval": {"type": "d"}})
    r(
        b"NSObject",
        b"getBoundingBoxMin:max:",
        {
            "required": True,
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type": b"^{SCNVector3=ddd}", "type_modifier": b"o"},
                3: {"type": b"^{SCNVector3=ddd}", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"getBoundingSphereCenter:radius:",
        {
            "required": True,
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type": b"^{SCNVector3=ddd}", "type_modifier": b"o"},
                3: {"type": b"^d", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"handleBindingOfSymbol:usingBlock:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"I"},
                            2: {"type": b"I"},
                            3: {"type": b"@"},
                            4: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleUnbindingOfSymbol:usingBlock:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"I"},
                            2: {"type": b"I"},
                            3: {"type": b"@"},
                            4: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(b"NSObject", b"hasActions", {"required": True, "retval": {"type": b"Z"}})
    r(
        b"NSObject",
        b"hitTest:options:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"{CGPoint=dd}"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"isAnimationForKeyPaused:",
        {"required": True, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"isJitteringEnabled", {"required": True, "retval": {"type": b"Z"}})
    r(
        b"NSObject",
        b"isNodeInsideFrustum:withPointOfView:",
        {
            "required": True,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(b"NSObject", b"isPlaying", {"required": True, "retval": {"type": b"Z"}})
    r(
        b"NSObject",
        b"isTemporalAntialiasingEnabled",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(b"NSObject", b"loops", {"required": True, "retval": {"type": b"Z"}})
    r(
        b"NSObject",
        b"minimumLanguageVersion",
        {"required": False, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"nodesInsideFrustumWithPointOfView:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"overlaySKScene", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"panSensitivity", {"required": True, "retval": {"type": "d"}})
    r(
        b"NSObject",
        b"pauseAnimationForKey:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"physicsWorld:didBeginContact:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"physicsWorld:didEndContact:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"physicsWorld:didUpdateContact:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(b"NSObject", b"pointOfView", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"prepareObject:shouldAbortBlock:",
        {
            "required": True,
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"prepareObjects:withCompletionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"presentScene:withTransition:incomingPointOfView:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(b"NSObject", b"program", {"required": False, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"program:bindValueForSymbol:atLocation:programID:renderer:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"I"},
                5: {"type": b"I"},
                6: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"program:handleError:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"program:unbindValueForSymbol:atLocation:programID:renderer:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"I"},
                5: {"type": b"I"},
                6: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"programIsOpaque:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"projectPoint:",
        {
            "required": True,
            "retval": {"type": b"{SCNVector3=ddd}"},
            "arguments": {2: {"type": b"{SCNVector3=ddd}"}},
        },
    )
    r(
        b"NSObject",
        b"removeActionForKey:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"removeAllActions", {"required": True, "retval": {"type": b"v"}})
    r(b"NSObject", b"removeAllAnimations", {"required": True, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"removeAllAnimationsWithBlendOutDuration:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": "d"}}},
    )
    r(
        b"NSObject",
        b"removeAnimationForKey:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"removeAnimationForKey:blendOutDuration:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "d"}},
        },
    )
    r(
        b"NSObject",
        b"removeAnimationForKey:fadeOutDuration:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "d"}},
        },
    )
    r(
        b"NSObject",
        b"renderNode:renderer:arguments:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"renderer:didApplyAnimationsAtTime:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"d"}},
        },
    )
    r(
        b"NSObject",
        b"renderer:didApplyConstraintsAtTime:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"d"}},
        },
    )
    r(
        b"NSObject",
        b"renderer:didApplyConstraintsAtTime:atTime:",
        {"arguments": {4: {"type": "d"}}},
    )
    r(
        b"NSObject",
        b"renderer:didRenderScene:atTime:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": "d"}},
        },
    )
    r(
        b"NSObject",
        b"renderer:didSimulatePhysicsAtTime:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"d"}},
        },
    )
    r(
        b"NSObject",
        b"renderer:updateAtTime:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"d"}},
        },
    )
    r(
        b"NSObject",
        b"renderer:willRenderScene:atTime:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": "d"}},
        },
    )
    r(b"NSObject", b"renderingAPI", {"required": True, "retval": {"type": "Q"}})
    r(
        b"NSObject",
        b"resumeAnimationForKey:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"rotationSensitivity", {"required": True, "retval": {"type": "d"}})
    r(
        b"NSObject",
        b"runAction:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"runAction:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"runAction:forKey:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"runAction:forKey:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(b"NSObject", b"scene", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"sceneTime", {"required": True, "retval": {"type": b"d"}})
    r(
        b"NSObject",
        b"setAllowsTranslation:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSObject",
        b"setAudioListener:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setAutoSwitchToFreeCamera:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSObject",
        b"setAutoenablesDefaultLighting:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSObject",
        b"setBoundingBoxMin:max:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^{SCNVector3=ddd}", "type_modifier": b"n"},
                3: {"type": b"^{SCNVector3=ddd}", "type_modifier": b"n"},
            },
        },
    )
    r(b"NSObject", b"setColorPixelFormat:", {"arguments": {2: {"type": "Q"}}})
    r(
        b"NSObject",
        b"setCurrentTime:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"d"}}},
    )
    r(
        b"NSObject",
        b"setDebugOptions:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": "Q"}}},
    )
    r(
        b"NSObject",
        b"setDelegate:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"setDepthPixelFormat:", {"arguments": {2: {"type": "Q"}}})
    r(
        b"NSObject",
        b"setFlyModeVelocity:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": "d"}}},
    )
    r(
        b"NSObject",
        b"setJitteringEnabled:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSObject",
        b"setLoops:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSObject",
        b"setMinimumLanguageVersion:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setOverlaySKScene:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setPanSensitivity:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": "d"}}},
    )
    r(
        b"NSObject",
        b"setPlaying:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSObject",
        b"setPointOfView:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setProgram:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"setRenderingAPI:", {"arguments": {2: {"type": "Q"}}})
    r(
        b"NSObject",
        b"setRotationSensitivity:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": "d"}}},
    )
    r(
        b"NSObject",
        b"setScene:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setSceneTime:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"d"}}},
    )
    r(
        b"NSObject",
        b"setShaderModifiers:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setShowsStatistics:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSObject",
        b"setSpeed:forAnimationKey:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": "d"}, 3: {"type": b"@"}},
        },
    )
    r(b"NSObject", b"setStencilPixelFormat:", {"arguments": {2: {"type": "Q"}}})
    r(
        b"NSObject",
        b"setTechnique:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setTemporalAntialiasingEnabled:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSObject",
        b"setTruckSensitivity:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": "d"}}},
    )
    r(
        b"NSObject",
        b"setUsesReverseZ:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(b"NSObject", b"shaderModifiers", {"required": False, "retval": {"type": b"@"}})
    r(b"NSObject", b"showsStatistics", {"required": True, "retval": {"type": b"Z"}})
    r(b"NSObject", b"stencilPixelFormat", {"required": True, "retval": {"type": "Q"}})
    r(b"NSObject", b"technique", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"truckSensitivity", {"required": True, "retval": {"type": "d"}})
    r(
        b"NSObject",
        b"unprojectPoint:",
        {
            "required": True,
            "retval": {"type": b"{SCNVector3=ddd}"},
            "arguments": {2: {"type": b"{SCNVector3=ddd}"}},
        },
    )
    r(b"NSObject", b"usesReverseZ", {"required": True, "retval": {"type": b"Z"}})
    r(
        b"NSObject",
        b"workingColorSpace",
        {"required": True, "retval": {"type": b"^{CGColorSpace=}"}},
    )
    r(
        b"NSObject",
        b"writeBytes:length:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": "n^v", "c_array_length_in_arg": 3},
                3: {"type": "Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"writeImage:withSceneDocumentURL:originalImageURL:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"SCNAction",
        b"customActionWithDuration:actionBlock:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"d"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"SCNAction",
        b"playAudioSource:waitForCompletion:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"SCNAction",
        b"rotateToX:y:z:duration:shortestUnitArc:",
        {"arguments": {6: {"type": b"Z"}}},
    )
    r(
        b"SCNAction",
        b"runBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"SCNAction",
        b"runBlock:queue:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"SCNAction",
        b"setTimingFunction:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"f"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"f"}},
                    }
                }
            }
        },
    )
    r(
        b"SCNAction",
        b"timingFunction",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"f"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"f"}},
                }
            }
        },
    )
    r(
        b"SCNAnimation",
        b"animationDidStart",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}},
                }
            }
        },
    )
    r(
        b"SCNAnimation",
        b"animationDidStop",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}},
                }
            }
        },
    )
    r(b"SCNAnimation", b"autoreverses", {"retval": {"type": "Z"}})
    r(b"SCNAnimation", b"fillsBackward", {"retval": {"type": "Z"}})
    r(b"SCNAnimation", b"fillsForward", {"retval": {"type": "Z"}})
    r(b"SCNAnimation", b"isAdditive", {"retval": {"type": "Z"}})
    r(b"SCNAnimation", b"isAppliedOnCompletion", {"retval": {"type": "Z"}})
    r(b"SCNAnimation", b"isCumulative", {"retval": {"type": "Z"}})
    r(b"SCNAnimation", b"isRemovedOnCompletion", {"retval": {"type": "Z"}})
    r(b"SCNAnimation", b"setAdditive:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"SCNAnimation",
        b"setAnimationDidStart:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"SCNAnimation",
        b"setAnimationDidStop:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"Z"},
                        },
                    }
                }
            }
        },
    )
    r(b"SCNAnimation", b"setAppliedOnCompletion:", {"arguments": {2: {"type": "Z"}}})
    r(b"SCNAnimation", b"setAutoreverses:", {"arguments": {2: {"type": "Z"}}})
    r(b"SCNAnimation", b"setCumulative:", {"arguments": {2: {"type": "Z"}}})
    r(b"SCNAnimation", b"setFillsBackward:", {"arguments": {2: {"type": "Z"}}})
    r(b"SCNAnimation", b"setFillsForward:", {"arguments": {2: {"type": "Z"}}})
    r(b"SCNAnimation", b"setRemovedOnCompletion:", {"arguments": {2: {"type": "Z"}}})
    r(b"SCNAnimation", b"setUsesSceneTimeBase:", {"arguments": {2: {"type": "Z"}}})
    r(b"SCNAnimation", b"usesSceneTimeBase", {"retval": {"type": "Z"}})
    r(
        b"SCNAnimationEvent",
        b"animationEventWithKeyTime:block:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"Z"},
                        },
                    }
                }
            }
        },
    )
    r(b"SCNAnimationPlayer", b"paused", {"retval": {"type": "Z"}})
    r(b"SCNAnimationPlayer", b"setPaused:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"SCNAudioPlayer",
        b"didFinishPlayback",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}},
                }
            }
        },
    )
    r(
        b"SCNAudioPlayer",
        b"setDidFinishPlayback:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"SCNAudioPlayer",
        b"setWillStartPlayback:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"SCNAudioPlayer",
        b"willStartPlayback",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}},
                }
            }
        },
    )
    r(
        b"SCNAudioSource",
        b"didFinishPlayback",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}},
                }
            }
        },
    )
    r(b"SCNAudioSource", b"isPositional", {"retval": {"type": "Z"}})
    r(b"SCNAudioSource", b"loops", {"retval": {"type": b"Z"}})
    r(
        b"SCNAudioSource",
        b"setDidFinishPlayback:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(b"SCNAudioSource", b"setLoops:", {"arguments": {2: {"type": b"Z"}}})
    r(b"SCNAudioSource", b"setPositional:", {"arguments": {2: {"type": "Z"}}})
    r(b"SCNAudioSource", b"setShouldStream:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"SCNAudioSource",
        b"setWillStartPlayback:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(b"SCNAudioSource", b"shouldStream", {"retval": {"type": "Z"}})
    r(
        b"SCNAudioSource",
        b"willStartPlayback",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}},
                }
            }
        },
    )
    r(b"SCNCamera", b"automaticallyAdjustsZRange", {"retval": {"type": b"Z"}})
    r(b"SCNCamera", b"grainIsColored", {"retval": {"type": b"Z"}})
    r(
        b"SCNCamera",
        b"setAutomaticallyAdjustsZRange:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"SCNCamera", b"setGrainIsColored:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"SCNCamera",
        b"setUsesOrthographicProjection:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"SCNCamera", b"setWantsDepthOfField:", {"arguments": {2: {"type": "Z"}}})
    r(b"SCNCamera", b"setWantsExposureAdaptation:", {"arguments": {2: {"type": "Z"}}})
    r(b"SCNCamera", b"setWantsHDR:", {"arguments": {2: {"type": "Z"}}})
    r(b"SCNCamera", b"usesOrthographicProjection", {"retval": {"type": b"Z"}})
    r(b"SCNCamera", b"wantsDepthOfField", {"retval": {"type": "Z"}})
    r(b"SCNCamera", b"wantsExposureAdaptation", {"retval": {"type": "Z"}})
    r(b"SCNCamera", b"wantsHDR", {"retval": {"type": "Z"}})
    r(b"SCNCameraController", b"automaticTarget", {"retval": {"type": "Z"}})
    r(b"SCNCameraController", b"inertiaEnabled", {"retval": {"type": "Z"}})
    r(b"SCNCameraController", b"isInertiaRunning", {"retval": {"type": "Z"}})
    r(b"SCNCameraController", b"setAutomaticTarget:", {"arguments": {2: {"type": "Z"}}})
    r(b"SCNCameraController", b"setInertiaEnabled:", {"arguments": {2: {"type": "Z"}}})
    r(b"SCNConstraint", b"isEnabled", {"retval": {"type": "Z"}})
    r(b"SCNConstraint", b"isIncremental", {"retval": {"type": "Z"}})
    r(b"SCNConstraint", b"setEnabled:", {"arguments": {2: {"type": "Z"}}})
    r(b"SCNConstraint", b"setIncremental:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"SCNGeometry",
        b"setWantsAdaptiveSubdivision:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"SCNGeometry", b"wantsAdaptiveSubdivision", {"retval": {"type": "Z"}})
    r(
        b"SCNGeometryElement",
        b"geometryElementWithBuffer:primitiveType:primitiveCount:indicesChannelCount:interleavedIndicesChannels:bytesPerIndex:",
        {"arguments": {6: {"type": b"Z"}}},
    )
    r(
        b"SCNGeometryElement",
        b"geometryElementWithData:primitiveType:primitiveCount:indicesChannelCount:interleavedIndicesChannels:bytesPerIndex:",
        {"arguments": {6: {"type": b"Z"}}},
    )
    r(
        b"SCNGeometryElement",
        b"hasInterleavedIndicesChannels",
        {"retval": {"type": b"Z"}},
    )
    r(b"SCNGeometrySource", b"floatComponents", {"retval": {"type": b"Z"}})
    r(
        b"SCNGeometrySource",
        b"geometrySourceWithData:semantic:vectorCount:floatComponents:componentsPerVector:bytesPerComponent:dataOffset:dataStride:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(
        b"SCNGeometrySource",
        b"geometrySourceWithNormals:count:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"SCNGeometrySource",
        b"geometrySourceWithTextureCoordinates:count:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"SCNGeometrySource",
        b"geometrySourceWithVertices:count:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(b"SCNGeometryTessellator", b"isAdaptive", {"retval": {"type": "Z"}})
    r(b"SCNGeometryTessellator", b"isScreenSpace", {"retval": {"type": "Z"}})
    r(b"SCNGeometryTessellator", b"setAdaptive:", {"arguments": {2: {"type": "Z"}}})
    r(b"SCNGeometryTessellator", b"setScreenSpace:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"SCNHitTestResult",
        b"simdLocalCoordinates",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"SCNHitTestResult",
        b"simdLocalNormal",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"SCNHitTestResult",
        b"simdModelTransform",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
        },
    )
    r(
        b"SCNHitTestResult",
        b"simdWorldCoordinates",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"SCNHitTestResult",
        b"simdWorldNormal",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"SCNLight",
        b"areaExtents",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(b"SCNLight", b"automaticallyAdjustsShadowProjection", {"retval": {"type": "Z"}})
    r(b"SCNLight", b"castsShadow", {"retval": {"type": b"Z"}})
    r(b"SCNLight", b"doubleSided", {"retval": {"type": b"Z"}})
    r(b"SCNLight", b"drawsArea", {"retval": {"type": b"Z"}})
    r(b"SCNLight", b"forcesBackFaceCasters", {"retval": {"type": "Z"}})
    r(
        b"SCNLight",
        b"parallaxCenterOffset",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(b"SCNLight", b"parallaxCorrectionEnabled", {"retval": {"type": b"Z"}})
    r(
        b"SCNLight",
        b"parallaxExtentsFactor",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"SCNLight",
        b"probeExtents",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"SCNLight",
        b"probeOffset",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(b"SCNLight", b"sampleDistributedShadowMaps", {"retval": {"type": "Z"}})
    r(
        b"SCNLight",
        b"setAreaExtents:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"SCNLight",
        b"setAutomaticallyAdjustsShadowProjection:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"SCNLight", b"setCastsShadow:", {"arguments": {2: {"type": b"Z"}}})
    r(b"SCNLight", b"setDoubleSided:", {"arguments": {2: {"type": b"Z"}}})
    r(b"SCNLight", b"setDrawsArea:", {"arguments": {2: {"type": b"Z"}}})
    r(b"SCNLight", b"setForcesBackFaceCasters:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"SCNLight",
        b"setParallaxCenterOffset:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(b"SCNLight", b"setParallaxCorrectionEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"SCNLight",
        b"setParallaxExtentsFactor:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"SCNLight",
        b"setProbeExtents:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"SCNLight",
        b"setProbeOffset:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"SCNLight",
        b"setSampleDistributedShadowMaps:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"SCNLookAtConstraint", b"gimbalLockEnabled", {"retval": {"type": b"Z"}})
    r(
        b"SCNLookAtConstraint",
        b"setGimbalLockEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"SCNMaterial", b"isDoubleSided", {"retval": {"type": b"Z"}})
    r(b"SCNMaterial", b"isLitPerPixel", {"retval": {"type": b"Z"}})
    r(b"SCNMaterial", b"locksAmbientWithDiffuse", {"retval": {"type": b"Z"}})
    r(b"SCNMaterial", b"readsFromDepthBuffer", {"retval": {"type": b"Z"}})
    r(b"SCNMaterial", b"setDoubleSided:", {"arguments": {2: {"type": b"Z"}}})
    r(b"SCNMaterial", b"setLitPerPixel:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"SCNMaterial",
        b"setLocksAmbientWithDiffuse:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"SCNMaterial", b"setReadsFromDepthBuffer:", {"arguments": {2: {"type": b"Z"}}})
    r(b"SCNMaterial", b"setWritesToDepthBuffer:", {"arguments": {2: {"type": b"Z"}}})
    r(b"SCNMaterial", b"writesToDepthBuffer", {"retval": {"type": b"Z"}})
    r(
        b"SCNMaterialProperty",
        b"precomputedLightingEnvironmentContentsWithData:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"SCNMaterialProperty",
        b"precomputedLightingEnvironmentContentsWithURL:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"SCNMaterialProperty",
        b"precomputedLightingEnvironmentDataForContents:device:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"SCNMorpher", b"setUnifiesNormals:", {"arguments": {2: {"type": "Z"}}})
    r(b"SCNMorpher", b"unifiesNormals", {"retval": {"type": "Z"}})
    r(b"SCNNode", b"castsShadow", {"retval": {"type": b"Z"}})
    r(b"SCNNode", b"childNodeWithName:recursively:", {"arguments": {3: {"type": b"Z"}}})
    r(
        b"SCNNode",
        b"childNodesPassingTest:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"o^Z"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"SCNNode",
        b"enumerateChildNodesUsingBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"o^Z"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"SCNNode",
        b"enumerateHierarchyUsingBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"o^Z"},
                        },
                    }
                }
            }
        },
    )
    r(b"SCNNode", b"isHidden", {"retval": {"type": b"Z"}})
    r(b"SCNNode", b"isPaused", {"retval": {"type": b"Z"}})
    r(b"SCNNode", b"setCastsShadow:", {"arguments": {2: {"type": b"Z"}}})
    r(b"SCNNode", b"setHidden:", {"arguments": {2: {"type": b"Z"}}})
    r(b"SCNNode", b"setPaused:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"SCNNode",
        b"setSimdEulerAngles:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"SCNNode",
        b"setSimdOrientation:",
        {
            "full_signature": b"v@:{simd_quatf=<4f>}",
            "arguments": {2: {"type": b"{simd_quatf=<4f>}"}},
        },
    )
    r(
        b"SCNNode",
        b"setSimdPivot:",
        {
            "full_signature": b"v@:{simd_float4x4=[4<4f>]}",
            "arguments": {2: {"type": b"{simd_float4x4=[4<4f>]}"}},
        },
    )
    r(
        b"SCNNode",
        b"setSimdPosition:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"SCNNode",
        b"setSimdRotation:",
        {"full_signature": b"v@:<4f>", "arguments": {2: {"type": b"<4f>"}}},
    )
    r(
        b"SCNNode",
        b"setSimdScale:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"SCNNode",
        b"setSimdTransform:",
        {
            "full_signature": b"v@:{simd_float4x4=[4<4f>]}",
            "arguments": {2: {"type": b"{simd_float4x4=[4<4f>]}"}},
        },
    )
    r(
        b"SCNNode",
        b"setSimdWorldOrientation:",
        {
            "full_signature": b"v@:{simd_quatf=<4f>}",
            "arguments": {2: {"type": b"{simd_quatf=<4f>}"}},
        },
    )
    r(
        b"SCNNode",
        b"setSimdWorldPosition:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"SCNNode",
        b"setSimdWorldTransform:",
        {
            "full_signature": b"v@:{simd_float4x4=[4<4f>]}",
            "arguments": {2: {"type": b"{simd_float4x4=[4<4f>]}"}},
        },
    )
    r(
        b"SCNNode",
        b"simdConvertPosition:fromNode:",
        {
            "full_signature": b"<3f>@:<3f>@",
            "retval": {"type": b"<3f>"},
            "arguments": {2: {"type": b"<3f>"}},
        },
    )
    r(
        b"SCNNode",
        b"simdConvertPosition:toNode:",
        {
            "full_signature": b"<3f>@:<3f>@",
            "retval": {"type": b"<3f>"},
            "arguments": {2: {"type": b"<3f>"}},
        },
    )
    r(
        b"SCNNode",
        b"simdConvertTransform:fromNode:",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:{simd_float4x4=[4<4f>]}@",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
            "arguments": {2: {"type": b"{simd_float4x4=[4<4f>]}"}},
        },
    )
    r(
        b"SCNNode",
        b"simdConvertTransform:toNode:",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:{simd_float4x4=[4<4f>]}@",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
            "arguments": {2: {"type": b"{simd_float4x4=[4<4f>]}"}},
        },
    )
    r(
        b"SCNNode",
        b"simdConvertVector:fromNode:",
        {
            "full_signature": b"<3f>@:<3f>@",
            "retval": {"type": b"<3f>"},
            "arguments": {2: {"type": b"<3f>"}},
        },
    )
    r(
        b"SCNNode",
        b"simdConvertVector:toNode:",
        {
            "full_signature": b"<3f>@:<3f>@",
            "retval": {"type": b"<3f>"},
            "arguments": {2: {"type": b"<3f>"}},
        },
    )
    r(
        b"SCNNode",
        b"simdEulerAngles",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"SCNNode",
        b"simdLocalFront",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"SCNNode",
        b"simdLocalRight",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"SCNNode",
        b"simdLocalRotateBy:",
        {
            "full_signature": b"v@:{simd_quatf=<4f>}",
            "arguments": {2: {"type": b"{simd_quatf=<4f>}"}},
        },
    )
    r(
        b"SCNNode",
        b"simdLocalTranslateBy:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"SCNNode",
        b"simdLocalUp",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"SCNNode",
        b"simdLookAt:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"SCNNode",
        b"simdLookAt:up:localFront:",
        {
            "full_signature": b"v@:<3f><3f><3f>",
            "arguments": {
                2: {"type": b"<3f>"},
                3: {"type": b"<3f>"},
                4: {"type": b"<3f>"},
            },
        },
    )
    r(
        b"SCNNode",
        b"simdOrientation",
        {
            "full_signature": b"{simd_quatf=<4f>}@:",
            "retval": {"type": b"{simd_quatf=<4f>}"},
        },
    )
    r(
        b"SCNNode",
        b"simdPivot",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
        },
    )
    r(
        b"SCNNode",
        b"simdPosition",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"SCNNode",
        b"simdRotateBy:aroundTarget:",
        {
            "full_signature": b"v@:{simd_quatf=<4f>}<3f>",
            "arguments": {2: {"type": b"{simd_quatf=<4f>}"}, 3: {"type": b"<3f>"}},
        },
    )
    r(
        b"SCNNode",
        b"simdRotation",
        {"full_signature": b"<4f>@:", "retval": {"type": b"<4f>"}},
    )
    r(
        b"SCNNode",
        b"simdScale",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"SCNNode",
        b"simdTransform",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
        },
    )
    r(
        b"SCNNode",
        b"simdWorldFront",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"SCNNode",
        b"simdWorldOrientation",
        {
            "full_signature": b"{simd_quatf=<4f>}@:",
            "retval": {"type": b"{simd_quatf=<4f>}"},
        },
    )
    r(
        b"SCNNode",
        b"simdWorldPosition",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"SCNNode",
        b"simdWorldRight",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"SCNNode",
        b"simdWorldTransform",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
        },
    )
    r(
        b"SCNNode",
        b"simdWorldUp",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"SCNParticleSystem",
        b"addModifierForProperties:atStage:withBlock:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^^v"},
                            2: {"type": b"^Q"},
                            3: {"type": b"q"},
                            4: {"type": b"q"},
                            5: {"type": b"f"},
                        },
                    }
                }
            }
        },
    )
    r(b"SCNParticleSystem", b"affectedByGravity", {"retval": {"type": b"Z"}})
    r(b"SCNParticleSystem", b"affectedByPhysicsFields", {"retval": {"type": b"Z"}})
    r(
        b"SCNParticleSystem",
        b"handleEvent:forProperties:withBlock:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^^v"},
                            2: {"type": b"^Q"},
                            3: {"type": b"^Q"},
                            4: {"type": b"q"},
                        },
                    }
                }
            }
        },
    )
    r(b"SCNParticleSystem", b"isBlackPassEnabled", {"retval": {"type": b"Z"}})
    r(b"SCNParticleSystem", b"isLightingEnabled", {"retval": {"type": b"Z"}})
    r(b"SCNParticleSystem", b"isLocal", {"retval": {"type": b"Z"}})
    r(b"SCNParticleSystem", b"loops", {"retval": {"type": b"Z"}})
    r(b"SCNParticleSystem", b"particleDiesOnCollision", {"retval": {"type": b"Z"}})
    r(
        b"SCNParticleSystem",
        b"setAffectedByGravity:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"SCNParticleSystem",
        b"setAffectedByPhysicsFields:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"SCNParticleSystem", b"setBlackPassEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(b"SCNParticleSystem", b"setLightingEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(b"SCNParticleSystem", b"setLocal:", {"arguments": {2: {"type": b"Z"}}})
    r(b"SCNParticleSystem", b"setLoops:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"SCNParticleSystem",
        b"setParticleDiesOnCollision:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"SCNParticleSystem",
        b"setWritesToDepthBuffer:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"SCNParticleSystem", b"writesToDepthBuffer", {"retval": {"type": b"Z"}})
    r(b"SCNPhysicsBody", b"allowsResting", {"retval": {"type": b"Z"}})
    r(
        b"SCNPhysicsBody",
        b"applyForce:atPosition:impulse:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(b"SCNPhysicsBody", b"applyForce:impulse:", {"arguments": {3: {"type": b"Z"}}})
    r(b"SCNPhysicsBody", b"applyTorque:impulse:", {"arguments": {3: {"type": b"Z"}}})
    r(b"SCNPhysicsBody", b"isAffectedByGravity", {"retval": {"type": "Z"}})
    r(b"SCNPhysicsBody", b"isResting", {"retval": {"type": b"Z"}})
    r(b"SCNPhysicsBody", b"setAffectedByGravity:", {"arguments": {2: {"type": "Z"}}})
    r(b"SCNPhysicsBody", b"setAllowsResting:", {"arguments": {2: {"type": b"Z"}}})
    r(b"SCNPhysicsBody", b"setResting:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"SCNPhysicsBody",
        b"setUsesDefaultMomentOfInertia:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"SCNPhysicsBody", b"usesDefaultMomentOfInertia", {"retval": {"type": "Z"}})
    r(
        b"SCNPhysicsField",
        b"customFieldWithEvaluationBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"{SCNVector3=ddd}"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{SCNVector3=ddd}"},
                            2: {"type": b"{SCNVector3=ddd}"},
                            3: {"type": b"f"},
                            4: {"type": b"f"},
                            5: {"type": b"d"},
                        },
                    }
                }
            }
        },
    )
    r(b"SCNPhysicsField", b"isActive", {"retval": {"type": b"Z"}})
    r(b"SCNPhysicsField", b"isExclusive", {"retval": {"type": b"Z"}})
    r(b"SCNPhysicsField", b"setActive:", {"arguments": {2: {"type": b"Z"}}})
    r(b"SCNPhysicsField", b"setExclusive:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"SCNPhysicsField",
        b"setUsesEllipsoidalExtent:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"SCNPhysicsField", b"usesEllipsoidalExtent", {"retval": {"type": b"Z"}})
    r(
        b"SCNProgram",
        b"handleBindingOfBufferNamed:frequency:usingBlock:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                            4: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"SCNProgram", b"isOpaque", {"retval": {"type": b"Z"}})
    r(b"SCNProgram", b"setOpaque:", {"arguments": {2: {"type": b"Z"}}})
    r(b"SCNReferenceNode", b"isLoaded", {"retval": {"type": "Z"}})
    r(b"SCNReplicatorConstraint", b"replicatesOrientation", {"retval": {"type": "Z"}})
    r(b"SCNReplicatorConstraint", b"replicatesPosition", {"retval": {"type": "Z"}})
    r(b"SCNReplicatorConstraint", b"replicatesScale", {"retval": {"type": "Z"}})
    r(
        b"SCNReplicatorConstraint",
        b"setReplicatesOrientation:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"SCNReplicatorConstraint",
        b"setReplicatesPosition:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"SCNReplicatorConstraint",
        b"setReplicatesScale:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"SCNScene", b"isPaused", {"retval": {"type": b"Z"}})
    r(
        b"SCNScene",
        b"sceneWithURL:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"SCNScene", b"setPaused:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"SCNScene",
        b"setWantsScreenSpaceReflection:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"SCNScene", b"wantsScreenSpaceReflection", {"retval": {"type": b"Z"}})
    r(
        b"SCNScene",
        b"writeToURL:options:delegate:progressHandler:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"f"},
                            2: {"type": b"@"},
                            3: {"type": b"o^Z"},
                        },
                    }
                }
            },
        },
    )
    r(
        b"SCNSceneSource",
        b"entriesPassingTest:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"o^Z"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"SCNSceneSource",
        b"sceneWithOptions:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"SCNSceneSource",
        b"sceneWithOptions:statusHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"f"},
                            2: {"type": b"q"},
                            3: {"type": b"@"},
                            4: {"type": b"o^Z"},
                        },
                    }
                }
            }
        },
    )
    r(b"SCNSphere", b"isGeodesic", {"retval": {"type": b"Z"}})
    r(b"SCNSphere", b"setGeodesic:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"SCNTechnique",
        b"handleBindingOfSymbol:usingBlock:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"I"},
                            2: {"type": b"I"},
                            3: {"type": b"@"},
                            4: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"SCNText", b"isWrapped", {"retval": {"type": b"Z"}})
    r(b"SCNText", b"setWrapped:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"SCNTransaction",
        b"completionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}},
                }
            }
        },
    )
    r(b"SCNTransaction", b"disableActions", {"retval": {"type": b"Z"}})
    r(
        b"SCNTransaction",
        b"setCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(b"SCNTransaction", b"setDisableActions:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"SCNTransformConstraint",
        b"orientationConstraintInWorldSpace:withBlock:",
        {
            "arguments": {
                2: {"type": "Z"},
                3: {
                    "callable": {
                        "retval": {"type": b"{SCNVector4=dddd}"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"{SCNVector4=dddd}"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"SCNTransformConstraint",
        b"positionConstraintInWorldSpace:withBlock:",
        {
            "arguments": {
                2: {"type": "Z"},
                3: {
                    "callable": {
                        "retval": {"type": b"{SCNVector3=ddd}"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"{SCNVector3=ddd}"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"SCNTransformConstraint",
        b"transformConstraintInWorldSpace:withBlock:",
        {
            "arguments": {
                2: {"type": b"Z"},
                3: {
                    "callable": {
                        "retval": {"type": b"{CATransform3D=dddddddddddddddd}"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"{CATransform3D=dddddddddddddddd}"},
                        },
                    }
                },
            }
        },
    )
    r(b"SCNView", b"allowsCameraControl", {"retval": {"type": "Z"}})
    r(b"SCNView", b"drawableResizesAsynchronously", {"retval": {"type": b"Z"}})
    r(b"SCNView", b"rendersContinuously", {"retval": {"type": "Z"}})
    r(
        b"SCNView",
        b"setAllowsCameraControl:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"SCNView",
        b"setDrawableResizesAsynchronously:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"SCNView", b"setRendersContinuously:", {"arguments": {2: {"type": "Z"}}})
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector("SCNAudioPlayer", b"initWithAVAudioNode:")
objc.registerNewKeywordsFromSelector("SCNAudioPlayer", b"initWithSource:")
objc.registerNewKeywordsFromSelector("SCNAudioSource", b"initWithFileNamed:")
objc.registerNewKeywordsFromSelector("SCNAudioSource", b"initWithURL:")
objc.registerNewKeywordsFromSelector("SCNIKConstraint", b"initWithChainRootNode:")
objc.registerNewKeywordsFromSelector("SCNReferenceNode", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("SCNReferenceNode", b"initWithURL:")
objc.registerNewKeywordsFromSelector("SCNSceneSource", b"initWithData:options:")
objc.registerNewKeywordsFromSelector("SCNSceneSource", b"initWithURL:options:")
objc.registerNewKeywordsFromSelector("SCNView", b"initWithFrame:options:")
expressions = {}

# END OF FILE
