CoreServices/CarbonCore/__init__.py,sha256=HQQ51sWNh3F-T4dnLikU3Alte4C52UC348ZUd-1ajkc,1024
CoreServices/CarbonCore/__pycache__/__init__.cpython-313.pyc,,
CoreServices/CarbonCore/__pycache__/_metadata.cpython-313.pyc,,
CoreServices/CarbonCore/_metadata.py,sha256=r3EUIAvMsoPtpbNweGz8I6G20mkonqpxtqMAyMCZcQI,25782
CoreServices/DictionaryServices/__init__.py,sha256=PwbwHO9x_27Sg0fCJxlqE9STn5Yn3RCE7Gp-tNZJGpQ,899
CoreServices/DictionaryServices/__pycache__/__init__.cpython-313.pyc,,
CoreServices/DictionaryServices/__pycache__/_metadata.cpython-313.pyc,,
CoreServices/DictionaryServices/_metadata.py,sha256=5bp78kDnvj0JUWygivYDM0OKIJ8Qhiol0E9zlKYHzU4,1003
CoreServices/LaunchServices/__init__.py,sha256=TYB-5SbHM0wGgzb-rNqbEYgkeHly-N_Xw7OyJQJ0fpw,887
CoreServices/LaunchServices/__pycache__/__init__.cpython-313.pyc,,
CoreServices/LaunchServices/__pycache__/_metadata.cpython-313.pyc,,
CoreServices/LaunchServices/_metadata.py,sha256=RXziOZfL6bCnkKJUK9EhtWlBrQ7efi6-f9W0Cke7zsc,38268
CoreServices/Metadata/__init__.py,sha256=saNfnA7VdCvs7yGZoaHMQw9hK19d-ZNZuJhrhxMbSas,970
CoreServices/Metadata/__pycache__/__init__.cpython-313.pyc,,
CoreServices/Metadata/__pycache__/_metadata.cpython-313.pyc,,
CoreServices/Metadata/_metadata.py,sha256=lnoWuuSvkrebFlYEF0u_ebZ8TREQHS20PRmVqbT1NjM,15073
CoreServices/SearchKit/__init__.py,sha256=RkfNlqyG5vxMXYioh67d31fUZ0gdKxqL83BILY8TE70,4275
CoreServices/SearchKit/__pycache__/__init__.cpython-313.pyc,,
CoreServices/SearchKit/__pycache__/_metadata.cpython-313.pyc,,
CoreServices/SearchKit/_metadata.py,sha256=PdOcR2tMeLKRPTOpW8C6JEDAM9bFarRq0fnzSh5ECtE,11337
CoreServices/__init__.py,sha256=YmZITVl_rkwXbE3wcyWYfjekzqnTGmOtFKrsWs1BOKI,1217
CoreServices/__pycache__/__init__.cpython-313.pyc,,
CoreServices/_inlines.cpython-313-darwin.so,sha256=tX6YV9ysCmn4d6vsrm_p5PN73twSrWByoHgAfJtxrYc,67488
pyobjc_framework_coreservices-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_coreservices-11.1.dist-info/METADATA,sha256=BYiu7K6Mwdlibg-CcK9S3MTO3g5qQ_wapWwujiOKFJE,2606
pyobjc_framework_coreservices-11.1.dist-info/RECORD,,
pyobjc_framework_coreservices-11.1.dist-info/WHEEL,sha256=memlX0NSEQnmSMa3rcNWPnk4cttudwgAZx3qq8qO4ME,115
pyobjc_framework_coreservices-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_coreservices-11.1.dist-info/top_level.txt,sha256=Ey6Ylpurgiktt-ECuhcVfDXaAgzWuhNmez0avWtzcVE,13
