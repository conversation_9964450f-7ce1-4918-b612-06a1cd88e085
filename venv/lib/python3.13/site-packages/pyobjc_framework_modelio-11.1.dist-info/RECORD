ModelIO/_ModelIO.cpython-313-darwin.so,sha256=oSkOHgeqbu3AfmhRjlG08imZsb4v3HLPmHzrRh8PGdg,89360
ModelIO/__init__.py,sha256=MSjC4GYPE8uyUmEMeD1qmIRKcUaMs1YBEKJwkMXFGwg,1179
ModelIO/__pycache__/__init__.cpython-313.pyc,,
ModelIO/__pycache__/_metadata.cpython-313.pyc,,
ModelIO/_metadata.py,sha256=Fd8DRU3Jv2vUntyoeUdN6723EBBvPCx49cj8mtwOnx4,83240
pyobjc_framework_modelio-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_modelio-11.1.dist-info/METADATA,sha256=T3RPsR-D346nnT9Ya61p-rG3X2Vcz0RE1REf8EL7_As,2465
pyobjc_framework_modelio-11.1.dist-info/RECORD,,
pyobjc_framework_modelio-11.1.dist-info/WHEEL,sha256=memlX0NSEQnmSMa3rcNWPnk4cttudwgAZx3qq8qO4ME,115
pyobjc_framework_modelio-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_modelio-11.1.dist-info/top_level.txt,sha256=tlLqg26IgOSRxeNWwXKxJjKC7_NMPPXcKGazzayLCwY,8
