InstantMessage/__init__.py,sha256=e5ZRUCUVgwZ3ACLQ9bwAgnl7m6mp0WV-r_UKvKZLRsU,896
InstantMessage/__pycache__/__init__.cpython-313.pyc,,
InstantMessage/__pycache__/_metadata.cpython-313.pyc,,
InstantMessage/_metadata.py,sha256=YhWE-ydy0p2UBh58CDsEr_6AWuKJYPuNpsyNsHsGbrY,4323
pyobjc_framework_instantmessage-11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_instantmessage-11.1.dist-info/METADATA,sha256=11Hk0h-oTU4t9LvtwFW3IWJ9vx1EyiPnyBmWvv2m7tU,2795
pyobjc_framework_instantmessage-11.1.dist-info/RECORD,,
pyobjc_framework_instantmessage-11.1.dist-info/WHEEL,sha256=JNWh1Fm1UdwIQV075glCn4MVuCRs0sotJIq-J6rbxCU,109
pyobjc_framework_instantmessage-11.1.dist-info/licenses/LICENSE.txt,sha256=DKBLB5KNSHK52bsiGHygQm3Yv6sI8m6toJmacdyBqv8,1249
pyobjc_framework_instantmessage-11.1.dist-info/pyobjc-build-info.txt,sha256=OfQNPl9BwB9fSU4KMztzne_AW50mIPMwZ9jSbPyMI-g,82
pyobjc_framework_instantmessage-11.1.dist-info/top_level.txt,sha256=ZFFCOIggeoURVRjDTOl89tHkH3nLUaPSpdbr9ZE_R_c,15
