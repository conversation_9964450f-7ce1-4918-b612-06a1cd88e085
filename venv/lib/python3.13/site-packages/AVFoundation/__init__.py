"""
Python mapping for the AVFoundation framework.

This module does not contain docstrings for the wrapped code, check Apple's
documentation for details on how to use these functions and classes.
"""


def _setup():
    import sys

    import CoreAudio
    import CoreMedia
    import Foundation
    import objc
    from . import _AVFoundation, _metadata
    from ._inlines import _inline_list_

    dir_func, getattr_func = objc.createFrameworkDirAndGetattr(
        name="AVFoundation",
        frameworkIdentifier="com.apple.avfoundation",
        frameworkPath=objc.pathForFramework(
            "/System/Library/Frameworks/AVFoundation.framework"
        ),
        globals_dict=globals(),
        inline_list=_inline_list_,
        parents=(_AVFoundation, CoreAudio, CoreMedia, Foundation),
        metadict=_metadata.__dict__,
    )

    globals()["__dir__"] = dir_func
    globals()["__getattr__"] = getattr_func

    for cls, sel in (
        ("AVAssetDownloadTask", b"originalRequest"),
        ("AVAssetDownloadTask", b"currentRequest"),
        ("AVAssetDownloadTask", b"response"),
        ("AVAggregateAssetDownloadTask", b"originalRequest"),
        ("AVAggregateAssetDownloadTask", b"currentRequest"),
        ("AVAggregateAssetDownloadTask", b"response"),
        ("AVAssetDownloadURLSession", b"sharedSession"),
        ("AVAssetDownloadURLSession", b"sessionWithConfiguration:"),
        ("AVAssetDownloadURLSession", b"sessionWithConfiguration:delegate:"),
        ("AVAssetDownloadURLSession", b"dataTaskWithRequest:"),
        ("AVAssetDownloadURLSession", b"dataTaskWithURL:"),
        ("AVAssetDownloadURLSession", b"uploadTaskWithRequest:fromFile:"),
        ("AVAssetDownloadURLSession", b"uploadTaskWithRequest:fromData:"),
        ("AVAssetDownloadURLSession", b"uploadTaskWithStreamedRequest:"),
        ("AVAssetDownloadURLSession", b"downloadTaskWithRequest:"),
        ("AVAssetDownloadURLSession", b"downloadTaskWithURL:"),
        ("AVAssetDownloadURLSession", b"downloadTaskWithResumeData:"),
        ("AVAssetDownloadURLSession", b"dataTaskWithRequest:completionHandler:"),
        ("AVAssetDownloadURLSession", b"dataTaskWithURL:completionHandler:"),
        (
            "AVAssetDownloadURLSession",
            b"uploadTaskWithRequest:fromFile:completionHandler:",
        ),
        (
            "AVAssetDownloadURLSession",
            b"uploadTaskWithRequest:fromData:completionHandler:",
        ),
        ("AVAssetDownloadURLSession", b"downloadTaskWithRequest:completionHandler:"),
        ("AVAssetDownloadURLSession", b"downloadTaskWithURL:completionHandler:"),
        ("AVAssetDownloadURLSession", b"downloadTaskWithResumeData:completionHandler:"),
        ("AVAudioSourceNode", b"init"),
        ("AVAudioChannelLayout", b"init"),
        ("AVAudioEnvironmentDistanceAttenuationParameters", b"init"),
        ("AVAudioEnvironmentReverbParameters", b"init"),
        ("AVAudioSinkNode", b"init"),
        ("AVAudioConnectionPoint", b"init"),
        ("AVAudioApplication", b"init"),
        ("AVSpeechSynthesisProviderVoice", b"init"),
        ("AVSpeechSynthesisProviderVoice", b"new"),
        ("AVSpeechSynthesisProviderRequest", b"init"),
        ("AVSpeechSynthesisProviderRequest", b"new"),
        ("AVAudioRoutingArbiter", b"init"),
        ("AVAudioRoutingArbiter", b"new"),
        ("AVAudioInputNode", b"init"),
        ("AVAudioOutputNode", b"init"),
        ("AVAudioUnitEQFilterParameters", b"init"),
        ("AVAudioMixingDestination", b"init"),
        ("AVAssetCache", b"init"),
        ("AVAssetCache", b"new"),
        ("AVAssetPlaybackAssistant", b"init"),
        ("AVAssetPlaybackAssistant", b"new"),
        ("AVCaptureInput", b"init"),
        ("AVCaptureInput", b"new"),
        ("AVCaptureInputPort", b"init"),
        ("AVCaptureInputPort", b"new"),
        ("AVAssetDownloadTask", b"init"),
        ("AVAssetDownloadTask", b"new"),
        ("AVAssetDownloadConfiguration", b"init"),
        ("AVAssetDownloadConfiguration", b"new"),
        ("AVAggregateAssetDownloadTask", b"init"),
        ("AVAggregateAssetDownloadTask", b"new"),
        ("AVAssetDownloadURLSession", b"init"),
        ("AVAssetDownloadURLSession", b"new"),
        ("AVAssetWriterInput", b"init"),
        ("AVAssetWriterInput", b"new"),
        ("AVAssetWriterInputPassDescription", b"init"),
        ("AVAssetWriterInputPassDescription", b"new"),
        ("AVAssetWriterInputPixelBufferAdaptor", b"init"),
        ("AVAssetWriterInputPixelBufferAdaptor", b"new"),
        ("AVAssetWriterInputTaggedPixelBufferGroupAdaptor", b"init"),
        ("AVAssetWriterInputTaggedPixelBufferGroupAdaptor", b"new"),
        ("AVAssetWriterInputMetadataAdaptor", b"init"),
        ("AVAssetWriterInputMetadataAdaptor", b"new"),
        ("AVAssetWriterInputCaptionAdaptor", b"init"),
        ("AVAssetWriterInputCaptionAdaptor", b"new"),
        ("AVAssetWriter", b"init"),
        ("AVAssetWriter", b"new"),
        ("AVAssetWriterInputGroup", b"init"),
        ("AVAssetWriterInputGroup", b"new"),
        ("AVPortraitEffectsMatte", b"init"),
        ("AVPortraitEffectsMatte", b"new"),
        ("AVSampleCursor", b"init"),
        ("AVSampleCursor", b"new"),
        ("AVAssetImageGenerator", b"init"),
        ("AVAssetImageGenerator", b"new"),
        ("AVAssetVariant", b"init"),
        ("AVAssetVariant", b"new"),
        ("AVAssetVariantVideoAttributes", b"init"),
        ("AVAssetVariantVideoAttributes", b"new"),
        ("AVAssetVariantVideoLayoutAttributes", b"init"),
        ("AVAssetVariantVideoLayoutAttributes", b"new"),
        ("AVAssetVariantAudioAttributes", b"init"),
        ("AVAssetVariantAudioAttributes", b"new"),
        ("AVAssetVariantQualifier", b"init"),
        ("AVAssetVariantQualifier", b"new"),
        ("AVCoordinatedPlaybackSuspension", b"init"),
        ("AVCoordinatedPlaybackSuspension", b"new"),
        ("AVPlaybackCoordinator", b"init"),
        ("AVPlaybackCoordinator", b"new"),
        ("AVPlayerPlaybackCoordinator", b"init"),
        ("AVPlayerPlaybackCoordinator", b"new"),
        ("AVDelegatingPlaybackCoordinatorPlaybackControlCommand", b"init"),
        ("AVDelegatingPlaybackCoordinatorPlaybackControlCommand", b"new"),
        ("AVDelegatingPlaybackCoordinatorPlayCommand", b"init"),
        ("AVDelegatingPlaybackCoordinatorPlayCommand", b"new"),
        ("AVDelegatingPlaybackCoordinatorBufferingCommand", b"init"),
        ("AVDelegatingPlaybackCoordinatorBufferingCommand", b"new"),
        ("AVDelegatingPlaybackCoordinatorPauseCommand", b"init"),
        ("AVDelegatingPlaybackCoordinatorPauseCommand", b"new"),
        ("AVDelegatingPlaybackCoordinatorSeekCommand", b"init"),
        ("AVDelegatingPlaybackCoordinatorSeekCommand", b"new"),
        ("AVCaptionConversionValidator", b"init"),
        ("AVCaptionConversionValidator", b"new"),
        ("AVCaptionConversionWarning", b"init"),
        ("AVCaptionConversionWarning", b"new"),
        ("AVCaptionConversionAdjustment", b"init"),
        ("AVCaptionConversionAdjustment", b"new"),
        ("AVCaptionConversionTimeRangeAdjustment", b"init"),
        ("AVCaptionConversionTimeRangeAdjustment", b"new"),
        ("AVAssetTrackSegment", b"init"),
        ("AVAssetTrackSegment", b"new"),
        ("AVTextStyleRule", b"init"),
        ("AVTextStyleRule", b"new"),
        ("AVCaptureDataOutputSynchronizer", b"init"),
        ("AVCaptureDataOutputSynchronizer", b"new"),
        ("AVCaptureSynchronizedDataCollection", b"init"),
        ("AVCaptureSynchronizedDataCollection", b"new"),
        ("AVCaptureSynchronizedData", b"init"),
        ("AVCaptureSynchronizedData", b"new"),
        ("AVURLAsset", b"init"),
        ("AVURLAsset", b"new"),
        ("AVAssetSegmentReport", b"init"),
        ("AVAssetSegmentReport", b"new"),
        ("AVAssetSegmentTrackReport", b"init"),
        ("AVAssetSegmentTrackReport", b"new"),
        ("AVAssetSegmentReportSampleInformation", b"init"),
        ("AVAssetSegmentReportSampleInformation", b"new"),
        ("AVAssetReaderTrackOutput", b"init"),
        ("AVAssetReaderTrackOutput", b"new"),
        ("AVAssetReaderAudioMixOutput", b"init"),
        ("AVAssetReaderAudioMixOutput", b"new"),
        ("AVAssetReaderVideoCompositionOutput", b"init"),
        ("AVAssetReaderVideoCompositionOutput", b"new"),
        ("AVAssetReaderOutputMetadataAdaptor", b"init"),
        ("AVAssetReaderOutputMetadataAdaptor", b"new"),
        ("AVAssetReaderOutputCaptionAdaptor", b"init"),
        ("AVAssetReaderOutputCaptionAdaptor", b"new"),
        ("AVAssetReaderSampleReferenceOutput", b"init"),
        ("AVAssetReaderSampleReferenceOutput", b"new"),
        ("AVSemanticSegmentationMatte", b"init"),
        ("AVSemanticSegmentationMatte", b"new"),
        ("AVContentKeySession", b"init"),
        ("AVContentKeySession", b"new"),
        ("AVCaptureConnection", b"init"),
        ("AVCaptureConnection", b"new"),
        ("AVCaptureAudioChannel", b"init"),
        ("AVCaptureAudioChannel", b"new"),
        ("AVCaptureBracketedStillImageSettings", b"init"),
        ("AVCaptureBracketedStillImageSettings", b"new"),
        ("AVCaptureOutput", b"init"),
        ("AVCaptureOutput", b"new"),
        ("AVPlayerLooper", b"init"),
        ("AVPlayerLooper", b"new"),
        ("AVExternalStorageDevice", b"init"),
        ("AVExternalStorageDevice", b"new"),
        ("AVExternalStorageDeviceDiscoverySession", b"init"),
        ("AVExternalStorageDeviceDiscoverySession", b"new"),
        ("AVCameraCalibrationData", b"init"),
        ("AVCameraCalibrationData", b"new"),
        ("AVVideoPerformanceMetrics", b"init"),
        ("AVVideoPerformanceMetrics", b"new"),
        ("AVCaptionRendererScene", b"init"),
        ("AVCaptionRendererScene", b"new"),
        ("AVOutputSettingsAssistant", b"init"),
        ("AVOutputSettingsAssistant", b"new"),
        ("AVCaptureSystemPressureState", b"init"),
        ("AVCaptureSystemPressureState", b"new"),
        ("AVPlayerVideoOutput", b"init"),
        ("AVPlayerVideoOutput", b"new"),
        ("AVVideoOutputSpecification", b"init"),
        ("AVVideoOutputSpecification", b"new"),
        ("AVPlayerVideoOutputConfiguration", b"init"),
        ("AVPlayerVideoOutputConfiguration", b"new"),
        ("AVCapturePhotoOutputReadinessCoordinator", b"init"),
        ("AVCapturePhotoOutputReadinessCoordinator", b"new"),
        ("AVCaptureResolvedPhotoSettings", b"init"),
        ("AVCaptureResolvedPhotoSettings", b"new"),
        ("AVCapturePhoto", b"init"),
        ("AVCapturePhoto", b"new"),
        ("AVCaptureDeferredPhotoProxy", b"init"),
        ("AVCaptureDeferredPhotoProxy", b"new"),
        ("AVAssetReader", b"init"),
        ("AVAssetReader", b"new"),
        ("AVMetadataObject", b"init"),
        ("AVMetadataObject", b"new"),
        ("AVSampleBufferGenerator", b"init"),
        ("AVSampleBufferGenerator", b"new"),
        ("AVSampleBufferRequest", b"init"),
        ("AVSampleBufferRequest", b"new"),
        ("AVSampleBufferGeneratorBatch", b"init"),
        ("AVSampleBufferGeneratorBatch", b"new"),
        ("AVMediaDataStorage", b"init"),
        ("AVMediaDataStorage", b"new"),
        ("AVAssetExportSession", b"init"),
        ("AVAssetExportSession", b"new"),
        ("AVPlayerInterstitialEvent", b"init"),
        ("AVPlayerInterstitialEvent", b"new"),
        ("AVCaption", b"init"),
        ("AVCaption", b"new"),
        ("AVCaptionRuby", b"init"),
        ("AVCaptionRuby", b"new"),
        ("AVAssetTrack", b"init"),
        ("AVAssetTrack", b"new"),
        ("AVCaptionFormatConformer", b"init"),
        ("AVCaptionFormatConformer", b"new"),
        ("AVCaptureDevice", b"init"),
        ("AVCaptureDevice", b"new"),
        ("AVCaptureDeviceDiscoverySession", b"init"),
        ("AVCaptureDeviceDiscoverySession", b"new"),
        ("AVCaptureDeviceRotationCoordinator", b"init"),
        ("AVCaptureDeviceRotationCoordinator", b"new"),
        ("AVFrameRateRange", b"init"),
        ("AVFrameRateRange", b"new"),
        ("AVZoomRange", b"init"),
        ("AVZoomRange", b"new"),
        ("AVCaptureDeviceFormat", b"init"),
        ("AVCaptureDeviceFormat", b"new"),
        ("AVCaptureDeviceInputSource", b"init"),
        ("AVCaptureDeviceInputSource", b"new"),
        ("AVPlayerItem", b"init"),
        ("AVPlayerItem", b"new"),
        ("AVPlayerItemAccessLog", b"init"),
        ("AVPlayerItemAccessLog", b"new"),
        ("AVPlayerItemErrorLog", b"init"),
        ("AVPlayerItemAccessLogEvent", b"new"),
        ("AVPlayerItemAccessLogEvent", b"init"),
        ("AVPlayerItemErrorLogEvent", b"new"),
        ("AVPlayerItemErrorLogEvent", b"init"),
        ("AVAssetResourceLoader", b"new"),
        ("AVAssetResourceLoader", b"init"),
        ("AVAssetResourceLoadingRequestor", b"new"),
        ("AVAssetResourceLoadingRequestor", b"init"),
        ("AVAssetResourceLoadingRequest", b"new"),
        ("AVAssetResourceLoadingRequest", b"init"),
        ("AVAssetResourceLoadingContentInformationRequest", b"new"),
        ("AVAssetResourceLoadingContentInformationRequest", b"init"),
        ("AVAssetResourceLoadingDataRequest", b"new"),
        ("AVAssetResourceLoadingDataRequest", b"init"),
        ("AVContinuityDevice", b"new"),
        ("AVContinuityDevice", b"init"),
        ("AVDepthData", b"new"),
        ("AVDepthData", b"init"),
        ("AVCaptureSession", b"new"),
        ("AVCaptureSession", b"init"),
        ("AVExposureBiasRange", b"new"),
        ("AVExposureBiasRange", b"init"),
    ):
        objc.registerUnavailableMethod(cls, sel)

    del sys.modules["AVFoundation._metadata"]


globals().pop("_setup")()
