#!/usr/bin/env python3
"""
Journey to the West 5-Step Learning Video Generator
"""

import os
import sys
import json
import re
import subprocess
import requests
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import pyttsx3
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class VideoGenerator:
    def __init__(self):
        self.api_key = os.getenv('API_KEY')
        self.base_url = "https://api.siliconflow.cn/v1/chat/completions"
        self.output_dir = Path("output")
        self.temp_dir = Path("temp")

        # Create directories
        self.output_dir.mkdir(exist_ok=True)
        self.temp_dir.mkdir(exist_ok=True)

        # Initialize TTS
        self.tts = pyttsx3.init()
        self.setup_tts()

    def setup_tts(self):
        """Setup TTS with English female voice"""
        voices = self.tts.getProperty('voices')

        # Try to find a female English voice
        female_voice = None
        for voice in voices:
            if 'female' in voice.name.lower() or 'woman' in voice.name.lower():
                if 'english' in voice.name.lower() or 'en' in voice.id.lower():
                    female_voice = voice
                    break

        if female_voice:
            self.tts.setProperty('voice', female_voice.id)
        else:
            # Fallback to first available voice
            if voices:
                self.tts.setProperty('voice', voices[0].id)

        # Set speech rate and volume
        self.tts.setProperty('rate', 150)  # Speed
        self.tts.setProperty('volume', 0.9)  # Volume

    def generate_tts(self, text, output_path):
        """Generate TTS audio file"""
        try:
            self.tts.save_to_file(text, str(output_path))
            self.tts.runAndWait()
            return True
        except Exception as e:
            print(f"TTS generation failed: {e}")
            return False

    def parse_srt(self, srt_path):
        """Parse SRT subtitle file"""
        subtitles = []

        with open(srt_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Split by double newlines to get subtitle blocks
        blocks = re.split(r'\n\s*\n', content.strip())

        for block in blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                # Parse time range
                time_line = lines[1]
                time_match = re.match(r'(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})', time_line)
                if time_match:
                    start_time = time_match.group(1)
                    end_time = time_match.group(2)
                    text = ' '.join(lines[2:])

                    subtitles.append({
                        'start': start_time,
                        'end': end_time,
                        'text': text
                    })

        return subtitles

    def parse_quiz(self, quiz_path):
        """Parse quiz questions from txt file"""
        questions = []

        with open(quiz_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        current_question = None
        current_options = []

        for line in lines[2:]:  # Skip title and empty line
            line = line.strip()
            if not line:
                continue

            # Check if it's a question (starts with number)
            if re.match(r'^\d+\.', line):
                # Save previous question if exists
                if current_question and current_options:
                    questions.append({
                        'question': current_question,
                        'options': current_options.copy()
                    })

                current_question = line
                current_options = []
            elif re.match(r'^[ABC]\.', line):
                current_options.append(line)

        # Don't forget the last question
        if current_question and current_options:
            questions.append({
                'question': current_question,
                'options': current_options.copy()
            })

        return questions

    def generate_ai_answers(self, questions, subtitles):
        """Generate answers using AI and identify key frames"""

        # Prepare subtitle text
        subtitle_text = '\n'.join([f"{sub['start']}-{sub['end']}: {sub['text']}" for sub in subtitles])

        # Prepare questions text
        questions_text = ""
        for i, q in enumerate(questions, 1):
            questions_text += f"Question {i}: {q['question']}\n"
            for option in q['options']:
                questions_text += f"  {option}\n"
            questions_text += "\n"

        prompt = f"""Based on the following video subtitles and quiz questions, please:
1. Determine the correct answer (A, B, or C) for each question
2. Identify the specific time range in the subtitles that contains the key information for each answer
3. Provide a brief explanation for each answer

Video Subtitles:
{subtitle_text}

Quiz Questions:
{questions_text}

Please respond in JSON format:
{{
  "answers": [
    {{
      "question_number": 1,
      "correct_answer": "A",
      "explanation": "brief explanation",
      "key_timeframe": {{
        "start": "00:00:11,134",
        "end": "00:00:19,631"
      }}
    }}
  ]
}}
"""

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "model": "Qwen/Qwen3-32B",
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.1
        }

        try:
            response = requests.post(self.base_url, headers=headers, json=data)
            response.raise_for_status()

            result = response.json()
            ai_response = result['choices'][0]['message']['content']

            # Try to extract JSON from the response
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                print("Could not parse AI response as JSON")
                return None

        except Exception as e:
            print(f"AI API call failed: {e}")
            return None

    def create_question_image(self, question_data, question_num, output_path):
        """Create question image with white background"""

        # Image settings
        width, height = 1920, 1080
        background_color = (255, 255, 255)  # White
        text_color = (0, 0, 0)  # Black

        # Create image
        img = Image.new('RGB', (width, height), background_color)
        draw = ImageDraw.Draw(img)

        try:
            # Try to load a font
            font_large = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 60)
            font_medium = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 45)
        except:
            # Fallback to default font
            font_large = ImageFont.load_default()
            font_medium = ImageFont.load_default()

        # Draw question
        question_text = f"Question {question_num}"
        question_bbox = draw.textbbox((0, 0), question_text, font=font_large)
        question_width = question_bbox[2] - question_bbox[0]
        draw.text(((width - question_width) // 2, 150), question_text, fill=text_color, font=font_large)

        # Draw question content
        question_content = question_data['question']
        content_bbox = draw.textbbox((0, 0), question_content, font=font_medium)
        content_width = content_bbox[2] - content_bbox[0]
        draw.text(((width - content_width) // 2, 250), question_content, fill=text_color, font=font_medium)

        # Draw options
        y_offset = 400
        for option in question_data['options']:
            option_bbox = draw.textbbox((0, 0), option, font=font_medium)
            option_width = option_bbox[2] - option_bbox[0]
            draw.text(((width - option_width) // 2, y_offset), option, fill=text_color, font=font_medium)
            y_offset += 80

        # Save image
        img.save(output_path)
        return True

    def create_countdown_video(self, duration=5):
        """Create countdown video"""
        countdown_path = self.temp_dir / "countdown.mp4"

        # Create countdown using FFmpeg
        cmd = [
            'ffmpeg', '-y',
            '-f', 'lavfi',
            '-i', f'color=white:size=1920x1080:duration={duration}',
            '-vf', f'drawtext=fontfile=/System/Library/Fonts/Arial.ttf:text=%{{eif\\:({duration}-t)\\:d}}:fontsize=120:fontcolor=black:x=(w-text_w)/2:y=(h-text_h)/2',
            '-r', '30',
            str(countdown_path)
        ]

        try:
            subprocess.run(cmd, check=True, capture_output=True)
            return countdown_path
        except subprocess.CalledProcessError as e:
            print(f"Countdown creation failed: {e}")
            return None

    def extract_video_segment(self, video_path, start_time, end_time, output_path):
        """Extract video segment based on time range"""
        cmd = [
            'ffmpeg', '-y',
            '-i', str(video_path),
            '-ss', start_time,
            '-to', end_time,
            '-c', 'copy',
            str(output_path)
        ]

        try:
            subprocess.run(cmd, check=True, capture_output=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"Video segment extraction failed: {e}")
            return False

    def burn_subtitles(self, video_path, srt_path, output_path):
        """Burn subtitles into video"""
        cmd = [
            'ffmpeg', '-y',
            '-i', str(video_path),
            '-vf', f'subtitles={srt_path}',
            '-c:a', 'copy',
            str(output_path)
        ]

        try:
            subprocess.run(cmd, check=True, capture_output=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"Subtitle burning failed: {e}")
            return False

    def create_image_video(self, image_path, duration, output_path):
        """Create video from static image"""
        cmd = [
            'ffmpeg', '-y',
            '-loop', '1',
            '-i', str(image_path),
            '-t', str(duration),
            '-pix_fmt', 'yuv420p',
            '-r', '30',
            str(output_path)
        ]

        try:
            subprocess.run(cmd, check=True, capture_output=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"Image video creation failed: {e}")
            return False

    def combine_audio_video(self, video_path, audio_path, output_path):
        """Combine video with audio"""
        cmd = [
            'ffmpeg', '-y',
            '-i', str(video_path),
            '-i', str(audio_path),
            '-c:v', 'copy',
            '-c:a', 'aac',
            '-shortest',
            str(output_path)
        ]

        try:
            subprocess.run(cmd, check=True, capture_output=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"Audio-video combination failed: {e}")
            return False

    def concatenate_videos(self, video_list, output_path):
        """Concatenate multiple videos"""
        # Create file list for FFmpeg
        filelist_path = self.temp_dir / "filelist.txt"

        with open(filelist_path, 'w') as f:
            for video in video_list:
                f.write(f"file '{os.path.abspath(video)}'\n")

        cmd = [
            'ffmpeg', '-y',
            '-f', 'concat',
            '-safe', '0',
            '-i', str(filelist_path),
            '-c', 'copy',
            str(output_path)
        ]

        try:
            subprocess.run(cmd, check=True, capture_output=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"Video concatenation failed: {e}")
            return False

    def generate_episode_video(self, episode_number):
        """Generate complete video for one episode"""

        print(f"Generating video for episode {episode_number:03d}...")

        # File paths
        episode_prefix = f"{episode_number:03d}_Journey to the West {episode_number}"
        video_file = None
        srt_file = None
        quiz_file = None

        # Find matching files
        for file in Path('.').glob(f"{episode_prefix}*"):
            if file.suffix == '.mp4':
                video_file = file
            elif file.suffix == '.srt':
                srt_file = file

        quiz_file = Path(f"quizs/{episode_prefix}_*.txt")
        quiz_matches = list(Path('quizs').glob(f"{episode_prefix}_*.txt"))
        if quiz_matches:
            quiz_file = quiz_matches[0]

        if not all([video_file, srt_file, quiz_file]):
            print(f"Missing files for episode {episode_number}")
            print(f"Video: {video_file}")
            print(f"SRT: {srt_file}")
            print(f"Quiz: {quiz_file}")
            return False

        print(f"Using files:")
        print(f"  Video: {video_file}")
        print(f"  SRT: {srt_file}")
        print(f"  Quiz: {quiz_file}")

        # Parse input files
        print("Parsing quiz questions...")
        questions = self.parse_quiz(quiz_file)
        print(f"Found {len(questions)} questions")

        print("Parsing subtitles...")
        subtitles = self.parse_srt(srt_file)
        print(f"Found {len(subtitles)} subtitle entries")

        # Generate AI answers
        print("Generating AI answers...")
        ai_answers = self.generate_ai_answers(questions, subtitles)
        if not ai_answers:
            print("Failed to generate AI answers")
            return False

        print(f"Generated {len(ai_answers.get('answers', []))} answers")

        # Create temporary files
        video_segments = []

        # 1. Cover image (as first frame)
        print("Creating cover...")
        cover_video = self.temp_dir / "01_cover.mp4"
        if self.create_image_video("imgs/0.cover.png", 0.1, cover_video):
            video_segments.append(cover_video)

        # 2. No subtitles section
        print("Creating no-subtitles section...")
        no_sub_prompt = self.temp_dir / "02_no_sub_prompt.mp4"
        if self.create_image_video("imgs/1.不带字幕听.png", 3, no_sub_prompt):
            video_segments.append(no_sub_prompt)

        # Original video without subtitles
        video_segments.append(video_file)

        # 3. Quiz section
        print("Creating quiz section...")
        quiz_prompt = self.temp_dir / "03_quiz_prompt.mp4"
        if self.create_image_video("imgs/2.完成阅读理解.png", 3, quiz_prompt):
            video_segments.append(quiz_prompt)

        # Generate question videos
        for i, question in enumerate(questions, 1):
            print(f"Processing question {i}...")

            # Create question image
            question_img = self.temp_dir / f"question_{i}.png"
            self.create_question_image(question, i, question_img)

            # Generate TTS audio
            question_text = f"{question['question']} "
            for option in question['options']:
                question_text += f"{option} "

            question_audio = self.temp_dir / f"question_{i}.wav"
            self.generate_tts(question_text, question_audio)

            # Create question video
            question_video = self.temp_dir / f"question_{i}.mp4"
            question_img_video = self.temp_dir / f"question_{i}_img.mp4"

            # Create image video for the duration of audio
            if self.create_image_video(question_img, 10, question_img_video):  # 10 seconds should be enough
                if self.combine_audio_video(question_img_video, question_audio, question_video):
                    video_segments.append(question_video)

            # Add countdown
            countdown_video = self.create_countdown_video(5)
            if countdown_video:
                video_segments.append(countdown_video)

        # 4. With subtitles section
        print("Creating with-subtitles section...")
        with_sub_prompt = self.temp_dir / "04_with_sub_prompt.mp4"
        if self.create_image_video("imgs/3.带字幕听.png", 3, with_sub_prompt):
            video_segments.append(with_sub_prompt)

        # Video with subtitles
        with_sub_video = self.temp_dir / "with_subtitles.mp4"
        if self.burn_subtitles(video_file, srt_file, with_sub_video):
            video_segments.append(with_sub_video)

        # 5. Answer checking section
        print("Creating answer checking section...")
        answer_prompt = self.temp_dir / "05_answer_prompt.mp4"
        if self.create_image_video("imgs/4.阅读理解答案.png", 3, answer_prompt):
            video_segments.append(answer_prompt)

        # Process answers and key frames
        if 'answers' in ai_answers:
            for answer in ai_answers['answers']:
                print(f"Processing answer {answer.get('question_number', '?')}...")

                # Create answer display (simple text for now)
                answer_text = f"Question {answer.get('question_number', '?')}: {answer.get('correct_answer', '?')}"
                answer_audio = self.temp_dir / f"answer_{answer.get('question_number', '?')}.wav"
                self.generate_tts(answer_text, answer_audio)

                # Create simple answer image
                answer_img = self.temp_dir / f"answer_{answer.get('question_number', '?')}.png"
                self.create_simple_text_image(answer_text, answer_img)

                answer_video = self.temp_dir / f"answer_{answer.get('question_number', '?')}.mp4"
                answer_img_video = self.temp_dir / f"answer_{answer.get('question_number', '?')}_img.mp4"

                if self.create_image_video(answer_img, 3, answer_img_video):
                    if self.combine_audio_video(answer_img_video, answer_audio, answer_video):
                        video_segments.append(answer_video)

                # Extract key frame if available
                if 'key_timeframe' in answer:
                    key_start = answer['key_timeframe'].get('start')
                    key_end = answer['key_timeframe'].get('end')
                    if key_start and key_end:
                        key_segment = self.temp_dir / f"key_segment_{answer.get('question_number', '?')}.mp4"
                        if self.extract_video_segment(with_sub_video, key_start, key_end, key_segment):
                            video_segments.append(key_segment)

        # 6. Follow-along section
        print("Creating follow-along section...")
        follow_prompt = self.temp_dir / "06_follow_prompt.mp4"
        if self.create_image_video("imgs/5.看字幕跟读.png", 3, follow_prompt):
            video_segments.append(follow_prompt)

        # Add subtitled video again for follow-along
        video_segments.append(with_sub_video)

        # 7. Ending
        print("Creating ending...")
        ending_video = self.temp_dir / "07_ending.mp4"
        if self.create_image_video("imgs/6.视频结尾.png", 3, ending_video):
            video_segments.append(ending_video)

        # Concatenate all segments
        print("Concatenating all segments...")
        output_file = self.output_dir / f"{episode_prefix}_5step_learning.mp4"

        if self.concatenate_videos(video_segments, output_file):
            print(f"Successfully generated: {output_file}")
            return True
        else:
            print("Failed to concatenate video segments")
            return False

    def create_simple_text_image(self, text, output_path):
        """Create simple text image"""
        width, height = 1920, 1080
        background_color = (255, 255, 255)  # White
        text_color = (0, 0, 0)  # Black

        img = Image.new('RGB', (width, height), background_color)
        draw = ImageDraw.Draw(img)

        try:
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 80)
        except:
            font = ImageFont.load_default()

        # Calculate text position
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        x = (width - text_width) // 2
        y = (height - text_height) // 2

        draw.text((x, y), text, fill=text_color, font=font)
        img.save(output_path)
        return True


def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python video_generator.py <episode_number>")
        print("Example: python video_generator.py 2")
        sys.exit(1)

    try:
        episode_number = int(sys.argv[1])
    except ValueError:
        print("Episode number must be an integer")
        sys.exit(1)

    if not (1 <= episode_number <= 108):
        print("Episode number must be between 1 and 108")
        sys.exit(1)

    # Check if required files exist
    required_files = [
        "imgs/0.cover.png",
        "imgs/1.不带字幕听.png",
        "imgs/2.完成阅读理解.png",
        "imgs/3.带字幕听.png",
        "imgs/4.阅读理解答案.png",
        "imgs/5.看字幕跟读.png",
        "imgs/6.视频结尾.png"
    ]

    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)

    if missing_files:
        print("Missing required files:")
        for file in missing_files:
            print(f"  {file}")
        sys.exit(1)

    # Initialize generator
    generator = VideoGenerator()

    # Generate video
    success = generator.generate_episode_video(episode_number)

    if success:
        print(f"\n✅ Successfully generated video for episode {episode_number}")
    else:
        print(f"\n❌ Failed to generate video for episode {episode_number}")
        sys.exit(1)


if __name__ == "__main__":
    main()
