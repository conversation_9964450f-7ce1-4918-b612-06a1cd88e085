# Journey to the West 5 步法跟练视频制作

## 课程介绍
【5 步法跟练】西游记英文版，英文动画 5 步法学习：
1. 不带字幕裸听
2. 做第一遍阅读理解
3. 带字幕听
4. 核对阅读理解答案
5. 打开字幕跟读一遍

## 素材介绍
视频总共 108 集，每集内容包括了视频（约 5 分钟）、字幕、测试问题和生词。

### 现有素材文件
- **视频文件**：`ddd_Journey to the West_具体名称.mp4`（其中 ddd 是 3 位数字，如 001、002）
- **字幕文件**：`ddd_Journey to the West_具体名称.srt`
- **测试问题**：已从 `01.Journey to the West-quiz.pdf` 提取到 `quizs/ddd_Journey to the West x_标题.txt`
- **生词文件**：`01.Journey to the West-words.pdf`（暂不使用）
- **提示图片**：
  - `0.cover.png` - 统一封面
  - `1.不带字幕听.png` - 不带字幕听提示
  - `2.完成阅读理解.png` - 阅读理解提示
  - `3.带字幕听.png` - 带字幕听提示
  - `4.核对阅读理解答案.png` - 核对答案提示
  - `5.带字幕跟读.png` - 跟读提示
  - `6.视频结尾.png` - 结尾提示

## 目标视频结构（每集）

### 1. 视频封面
- 使用 `0.cover.png` 作为首帧（不需要持续时间，仅作为首帧）

### 2. 不带字幕听
- 提示图片：`1.不带字幕听.png`（3秒）
- 原视频：`ddd_Journey to the West_具体名称.mp4`（无字幕）

### 3. 第一遍阅读理解
- 提示图片：`2.完成阅读理解.png`（3秒）
- 5个测试问题处理：
  - 从 `quizs/ddd_Journey to the West x_标题.txt` 读取问题
  - 英文女声TTS朗读问题和选项
  - 生成白底问题图片显示
  - 每题后5秒倒计时

### 4. 带字幕听
- 提示图片：`3.带字幕听.png`（3秒）
- 带字幕视频：将 `ddd_Journey to the West_具体名称.srt` 烧录到 `ddd_Journey to the West_具体名称.mp4`

### 5. 核对阅读理解答案
- 提示图片：`4.核对阅读理解答案.png`（3秒）
- AI自动答案生成：
  - 使用 SiliconFlow API 的 Qwen/QwQ-32B 模型
  - 分析测试问题和字幕内容生成正确答案
  - 识别答案对应的关键时间段
  - 显示正确答案
  - 播放关键帧片段（尽量短，仅包含答案关键部分）

### 6. 带字幕跟读
- 提示图片：`5.带字幕跟读.png`（3秒）

### 7. 视频结尾
- 结尾图片：`6.视频结尾.png`（3秒）

## 技术实现方案

### 技术栈
- **主要工具**：Python + FFmpeg
- **AI服务**：SiliconFlow API (Qwen/QwQ-32B)
- **TTS服务**：本地TTS，英文女声
- **输出格式**：1920×1080 MP4

### 开发阶段

#### 第一阶段：核心功能模块
1. **AI答案生成模块**
   - 集成 SiliconFlow API
   - 设计prompt分析问题和字幕
   - 生成答案并识别关键时间段

2. **TTS语音生成模块**
   - 本地英文女声TTS
   - 生成问题朗读音频

3. **图片生成模块**
   - 白底问题显示图片
   - 答案显示图片

#### 第二阶段：视频处理模块
1. **字幕处理**：解析SRT文件，提取时间戳
2. **视频分割**：根据AI识别的关键帧提取片段
3. **视频合成**：使用FFmpeg按顺序合成各部分

#### 第三阶段：测试和批量处理
1. **单集测试**：先实现完整的单集处理流程
2. **效果验证**：测试生成效果并调整参数
3. **批量处理**：处理所有108集

### 处理流程
1. 读取素材文件（视频、字幕、测试题）
2. 生成TTS音频文件
3. 生成问题图片
4. 调用AI生成答案和关键帧
5. 使用FFmpeg合成最终视频

## 项目状态
- ✅ 测试题提取完成（108个txt文件）
- ⏳ 开发视频生成脚本
- ⏳ 测试单集处理流程
- ⏳ 批量处理所有集数


