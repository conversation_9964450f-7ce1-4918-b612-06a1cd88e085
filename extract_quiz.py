#!/usr/bin/env python3
"""
Extract quiz questions from Journey to the West PDF and save them as individual txt files.
"""

import pdfplumber
import re
import os
from pathlib import Path


def extract_all_text(pdf_path):
    """Extract all text from PDF"""
    all_text = []

    with pdfplumber.open(pdf_path) as pdf:
        print(f"Processing PDF with {len(pdf.pages)} pages...")

        for i, page in enumerate(pdf.pages):
            text = page.extract_text()
            if text:
                all_text.append(text)
            if (i + 1) % 10 == 0:
                print(f"Processed {i + 1} pages...")

    return '\n'.join(all_text)


def parse_episodes(text):
    """Parse episodes from the extracted text"""
    episodes = []

    # Split by episode headers
    lines = text.split('\n')
    current_episode = None
    current_questions = []

    for line in lines:
        line = line.strip()

        # Check if this is an episode header
        episode_match = re.match(r'Journey to the West (\d+): (.+)', line)
        if episode_match:
            # Save previous episode if exists
            if current_episode and current_questions:
                episodes.append({
                    'number': current_episode['number'],
                    'title': current_episode['title'],
                    'questions': current_questions.copy()
                })

            # Start new episode
            current_episode = {
                'number': int(episode_match.group(1)),
                'title': episode_match.group(2)
            }
            current_questions = []
            continue

        # Skip empty lines and page numbers
        if not line or re.match(r'^\d+\s*/\s*\d+$', line):
            continue

        # Add content to current episode
        if current_episode:
            current_questions.append(line)

    # Don't forget the last episode
    if current_episode and current_questions:
        episodes.append({
            'number': current_episode['number'],
            'title': current_episode['title'],
            'questions': current_questions.copy()
        })

    return episodes


def clean_title(title):
    """Clean title for filename"""
    # Remove or replace characters that might cause issues
    title = title.replace(':', '')
    title = title.replace('/', '_')
    title = title.replace('\\', '_')
    title = title.replace('?', '')
    title = title.replace('*', '')
    title = title.replace('<', '')
    title = title.replace('>', '')
    title = title.replace('|', '')
    title = title.replace('"', '')
    return title.strip()


def save_episode_questions(episode, output_dir):
    """Save questions for a single episode"""
    episode_num = episode['number']
    title = clean_title(episode['title'])

    # Create filename: 001_Journey to the West 1_The Monkey.txt
    filename = f"{episode_num:03d}_Journey to the West {episode_num}_{title}.txt"
    filepath = output_dir / filename

    # Format questions
    content = f"Journey to the West {episode_num}: {episode['title']}\n\n"
    content += '\n'.join(episode['questions'])

    # Write to file
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"Saved: {filename}")
    return filename


def main():
    pdf_path = "01.Journey to the West-quiz.pdf"
    output_dir = Path("quizs")

    # Create output directory
    output_dir.mkdir(exist_ok=True)

    print("Extracting text from PDF...")
    text = extract_all_text(pdf_path)

    print("Parsing episodes...")
    episodes = parse_episodes(text)

    print(f"Found {len(episodes)} episodes")

    # Save each episode
    saved_files = []
    for episode in episodes:
        filename = save_episode_questions(episode, output_dir)
        saved_files.append(filename)

    print(f"\nCompleted! Saved {len(saved_files)} files to {output_dir}/")

    # Show first few episodes as examples
    print("\nFirst 5 episodes:")
    for i, episode in enumerate(episodes[:5]):
        print(f"  {episode['number']:3d}: {episode['title']}")

    if len(episodes) > 5:
        print("  ...")
        print(f"  {episodes[-1]['number']:3d}: {episodes[-1]['title']}")


if __name__ == "__main__":
    main()
