下面给你**4 套可落地的制作方案**（从“全自动量产”到“半自动可视化”），并给出**对比与选型建议**。每套方案都覆盖：批量处理 108 集、把测验题从 PDF 中抽取、做题朗读（TTS）、倒计时、字幕与拼接导出。你电脑已有 **Python** 与 **FFmpeg**，可按需再装依赖（我给出推荐清单）。

---

## 总体流程（每集）

1. **封面 3s** → 2. **提示图 1（不带字幕听）3s** → 3. **原视频（无字）**
2. **提示图 2（完成阅读理解）3s** → 5. **5 题逐题展示 +TTS 朗读 + 末尾 5s 倒计时**
3. **提示图 3（带字幕听）3s** → 7. **原视频 + 外挂 / 烧录 SRT**
4. **提示图 4（核对答案）3s** → 9. **答案展示（或仅字幕 / 旁白）**
5. **提示图 5（带字幕跟读）3s** → 11. **原视频 + 字幕（可降原音量便于跟读）**
6. **结尾图 3s**

> 假设 PDF 中每集题目标题形如 `Journey to the West x: 具体名称`，与 `ddd` 一一对应（若不严格对应，可通过标题模糊匹配）。

---

## 方案 A（推荐量产）：**Python 生成清单 + FFmpeg 滤镜批处理**

**核心思想**：用 Python 负责**解析 PDF、生成 TTS、产出每集的 FFmpeg 命令 /concat 清单**；最终由 FFmpeg 批量渲染，速度快、稳定、体量大时更可靠。

**需要安装**

* Python: `pymupdf`（解析测验 / 生词 PDF）、 `pydub`（或直接用 ffprobe 拿音频时长）
* **离线 TTS**：`piper`（音色较好且轻量）或 `espeak-ng`（更轻量），macOS 也可直接用 `say`
* 字体：`Noto Sans CJK`（确保 drawtext 显示中英文不乱码）
* FFmpeg（已具备）

**关键做法**

* 用 `PyMuPDF` 按标题切块，抽出**每集 5 题**（题干 + 选项 + 答案）。
* 为**每题生成 TTS 音频**（题干→选项），得到 `qN.wav`；计算 `音频时长 + 5s` 作为该题视频段时长。
* 生成**题目视频段**：

  * 背景可用纯色：`-f lavfi -i color=size=1920x1080:rate=30:color=white`
  * `drawtext` 渲染多行文本（从 `textfile` 读入自动换行）；
  * 将 `qN.wav` 作为主音轨；
  * **倒计时**：仅在段尾 5s 显示，`drawtext=text='%{eif\\:max(0,5-(t-(D-5)))\\:d}' : enable='gte(t, D-5)'`（`D` 为段总长，Python 预先替换）。
* **字幕段**：`-vf subtitles=ddd_xxx.srt:force_style='FontName=Noto Sans CJK SC,Fontsize=28,MarginV=40'`（或软字幕 `-i srt -c:s mov_text`）
* **跟读段**：同字幕段，但**降低原视频音量**便于跟读：`-af volume=0.6`（或更进一步做鸭叫压缩 sidechain，复杂度可选）。
* **全部片段用 concat filter 拼接**，一次编码输出，避免中间多次重编码损耗。

**优点**

* **最快、最稳、最可扩**；FFmpeg 渲染效率高；易并行（GNU parallel）。
* 输出参数（分辨率 / 码率 / 字体 / 字幕风格）集中可控，**可全局迭代**。
* 对 108 集量产成本最低。

**缺点**

* FFmpeg 滤镜表达式略“硬核”；
* 复杂字幕排版（题目过长分栏、滚动）需要多试几次调样式。

**最小落地骨架（片段）**

```bash
# 1) 提示图做 3s 段
ffmpeg -y -loop 1 -t 3 -i "1.不带字幕听.png" -vf "scale=1920:-1,format=yuv420p,fps=30" -an tip1.mp4

# 2) 原视频无字幕段
ffmpeg -y -i "001_Journey to the West_xxx.mp4" -c:v libx264 -c:a aac -shortest raw_nosub.mp4

# 3) 原视频带字幕段（烧录）
ffmpeg -y -i "001_Journey to the West_xxx.mp4" \
  -vf "subtitles='001_Journey to the West_xxx.srt':force_style='FontName=Noto Sans CJK SC,Fontsize=28,MarginV=40'" \
  -c:v libx264 -c:a aac raw_sub.mp4

# 4) 某一道题的视频（D 为 q1.wav 时长+5）
ffmpeg -y -f lavfi -i "color=size=1920x1080:rate=30:color=white" -i q1.wav \
  -vf "drawtext=fontfile='NotoSansCJKsc-Regular.otf':textfile='q1.txt':x=80:y=120:fontsize=42:line_spacing=16:fontcolor=black:box=1:boxcolor=white@0.0,\
       drawtext=fontfile='NotoSansCJKsc-Regular.otf':text='%{eif\\:max(0,5-(t-(D-5)))\\:d}':x=(w-tw-80):y=(h-th-80):fontsize=90:fontcolor=black:enable='gte(t, D-5)'" \
  -t D -c:v libx264 -c:a aac q1.mp4

# 5) 最终拼接（示例）
ffmpeg -y \
  -i cover3s.mp4 -i tip1.mp4 -i raw_nosub.mp4 -i tip2.mp4 -i q1.mp4 -i q2.mp4 -i q3.mp4 -i q4.mp4 -i q5.mp4 \
  -i tip3.mp4 -i raw_sub.mp4 -i tip4.mp4 -i answers.mp4 -i tip5.mp4 -i shadowing.mp4 -i ending3s.mp4 \
  -filter_complex "[0:v][0:a][1:v][1:a]...[15:v][15:a]concat=n=16:v=1:a=1[v][a]" \
  -map "[v]" -map "[a]" -r 30 -pix_fmt yuv420p -c:v libx264 -crf 18 -c:a aac -b:a 192k out_ep001.mp4
```

**Python 侧建议模块**

* `build_index.py`：扫描 `ddd_*.mp4/.srt`，产出 `episodes.csv (ddd,title,mp4,srt)`
* `parse_quiz.py`：从 `01.Journey to the West-quiz.pdf` 提取每集 5 题 JSON（题干 / 选项 / 答案）。
* `gen_tts.py`：按题生成 `qN.wav`；用 `piper` 或 `say`。
* `render_ffmpeg.py`：计算各段时长，生成并执行 FFmpeg 命令（或写 `.sh` 用 parallel 跑）。

---

## 方案 B：**MoviePy 全流程（Python 里合成全部视频）**

**核心思想**：用 `moviepy`（底层还是调用 ffmpeg）直接拼图、文字、音频、视频，代码风格友好，文本排版更灵活。

**优点**

* **开发体验友好**，复杂布局（多段落、分栏、题目滚动）更容易；
* 计时 / 动画控制在 Python 层做，**逻辑更直观**。

**缺点**

* 对 108 集×多片段，**渲染速度和内存占用较高**；
* 复杂长视频偶发崩溃，**稳定性不如纯 FFmpeg**；
* 并行调优麻烦些。

**适用**

* 需要更复杂排版 / 动画；批量量级不特别大，或能接受更长渲染时间。

---

## 方案 C：**Kdenlive/Shotcut 模板 + 脚本填充（MLT）**

**核心思想**：用 NLE（非编）做**一个标准模板工程**（含占位文本层、提示图位、字幕轨、倒计时层），然后用脚本把每集的文本与媒体路径写入 MLT/Kdenlive 工程 XML，再用 CLI 渲染。

**优点**

* 有 **所见即所得** 的可视化模板，非技术同事也能微调；
* 渲染用引擎 `melt/MLT`，稳定性不错。

**缺点**

* 前期**模板搭建**成本较高；
* 批量参数化需要写 XML 替换脚本，**维护成本**略高；
* 跑满 108 集仍不如 A 稳 / 快。

**适用**

* 需要可视化校对与个别集手工干预；
* 有非技术同事参与。

---

## 方案 D：**PPT/Keynote 生成“题目与答案段”视频 + FFmpeg 拼接**

**核心思想**：**问题与答案段**用 PPT 批量生成（VBA/Macros 把文本灌入模板，导出为 MP4），其余仍用 FFmpeg。TTS 可用 `say` / `piper` 另导音频后叠加。

**优点**

* **设计门槛最低**，排版最省心；
* 视觉一致性好。

**缺点**

* 自动化程度较 A/B 低；
* TTS 对齐与倒计时**同步难度**略高（需要导出时长与音频对齐）。

**适用**

* 追求“题目段”**美观**且**人手可操作**；自动化要求一般。

---

## 核心难点与解决要点

1. **PDF 解析与集数映射**

   * 用 `PyMuPDF` 根据标题正则分段；若 `x` 与 `ddd` 有偏差，先用标题“具体名称”做**模糊匹配**（比对 mp4 文件名中的标题部分）。
2. **TTS（离线）**

   * **优先**：`piper`（多语音包、音质较好、CPU 也可跑）；
   * 轻量：`espeak-ng`；
   * macOS：`say -v Alex` / `-v Ting-Ting`（中英混读可分段合成再拼）。
3. **题目文本渲染**

   * 使用 `drawtext textfile=`，保证 `UTF-8`；超长可**自动换行**或拆为上下两屏（Python 控制）。
4. **倒计时**

   * 以“音频时长 + 5”作为段时长，**仅段尾 5s**显示数字；
   * 对齐靠 Python 先取 `ffprobe` 音频长度。
5. **字幕**

   * 烧录更稳（统一风格），外挂更灵活（可选择开 / 关）；
   * 中英文显示请装 `Noto Sans CJK`，`force_style` 控边距字号。
6. **音量与跟读**

   * 跟读段建议 `-af volume=0.6`；如需说话时自动压主音，可上 `sidechaincompress`（复杂度↑）。
7. **性能**

   * A 方案可用 `GNU parallel` 分集并行；
   * 控制码率：`-crf 18~22`；音频 `aac 128~192k`；输出 1080p/30fps。

---

## 方案对比（简表）

| 方案                  | 自动化 | 批量稳定性 |  开发难度 |  渲染速度 |  复杂排版 | 适合场景         |
| ------------------- | --- | ----: | ----: | ----: | ----: | ------------ |
| **A FFmpeg+Python** | 高   | **高** |     中 | **快** |     中 | 大规模量产、标准化强   |
| **B MoviePy**       | 高   |     中 | **低** |   中 - 慢 | **高** | 复杂动画 / 排版、量级中等 |
| **C NLE 模板**        | 中   |   中 - 高 |     高 |     中 |     高 | 需可视化校对 / 模板化产线 |
| **D PPT+FFmpeg**    | 低 - 中 |     中 | **低** |     中 |     中 | 题目段视觉友好、半自动  |

---

## 建议选型

* **首选：方案 A**（你会写 Python & shell，追求稳定批量生产）。
* 若题目段排版要更花哨或滚动动画：**B**。
* 需要非技术同事可见可改：**C**。
* 快速出第一版、题目段好看：**D**。

---

## 快速起步清单（以方案 A 为例）

**目录建议**

```
project/
  assets/
    0.cover.png 1.不带字幕听.png 2.完成阅读理解.png 3.带字幕听.png 4.核对阅读理解答案.png 5.带字幕跟读.png 6.视频结尾.png
    fonts/NotoSansCJKsc-Regular.otf
  pdf/
    01.Journey to the West-quiz.pdf
    01.Journey to the West-words.pdf
  src/
    parse_quiz.py  gen_tts.py  render_ffmpeg.py  build_index.py  util.py
  media/
    videos/ (ddd_*.mp4)  subs/ (ddd_*.srt)
  out/
```

**依赖安装**

```bash
pip install pymupdf pydub
# macOS（任选其一）：
brew install piper # 或 espeak
brew install ffmpeg fontconfig
```

**示例：解析 PDF（提纲）**

```python
# parse_quiz.py
import fitz, re, json
pat = re.compile(r"Journey to the West\s+(\d+)\s*:\s*(.+)", re.I)

def extract(pdf_path):
    doc = fitz.open(pdf_path)
    blocks = {}  # {episode_number: {"title":..., "qa":[{"q":..., "opts":[...], "ans":"B"} *5]}}
    # 逐页扫描，遇到标题行就开新块；根据视觉块/段落合并题干与选项
    # ...（按你实际版式微调）
    return blocks

if __name__ == "__main__":
    data = extract("pdf/01.Journey to the West-quiz.pdf")
    with open("out/quiz.json", "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
```

**示例：生成 TTS（提纲）**

```python
# gen_tts.py
import subprocess, json, textwrap, os, shlex

def say_piper(text, wav_path, voice="en_US-amy-low.onnx"):
    cmd = f"piper --model {shlex.quote(voice)} --output_file {shlex.quote(wav_path)}"
    subprocess.run(cmd, input=text.encode("utf-8"), shell=True, check=True)

def build_question_text(q):
    # q: {"q": "...", "opts": ["A. ...", "B. ...", ...]}
    return "Question: " + q["q"] + "\n" + "\n".join(q["opts"])

# 根据 quiz.json 为每集每题生成 qN.wav 与 qN.txt（渲染文本）
```

**示例：计算音频时长**

```bash
ffprobe -v error -show_entries format=duration -of default=nw=1:nk=1 q1.wav
```

**示例：答案段（简单版）**

* 直接白底 + `drawtext` 列出 5 题答案；或 TTS 朗读答案。
* 若 PDF 提供解析，可追加简述（文本长度控 2\~3 行）。

---

## 待你确认的 6 个小问题

1. **分辨率 / 帧率**：是否统一 **1080p/30fps**？
2. **字幕**：烧录（固定风格）还是外挂（可开关）？默认我做烧录。
3. **TTS 音色**：男 / 女声？英文题干 / 选项，是否需要中英文混读（如中文提示改中文音色）？
4. **跟读段音量**：是否将原声降到 **-6 \~ -12 dB**？
5. **答案段**：只显示答案字母（A/B/C/D），还是需要短解析？
6. **是否加入“生词”环节**：例如在答案段后展示本集生词并 TTS 朗读？

你回复以上偏好，我就按**方案 A**给你一个**可直接运行的脚手架（Python 脚本 + 示例 FFmpeg 命令）**，从 1 集跑通到全量 108 集一键批处理。
